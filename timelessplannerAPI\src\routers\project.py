from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import project

router = APIRouter(prefix="/project", tags=["project"])

@router.post(
    "/create_project",
    response_model=schemas.ProjectRead,
    status_code=status.HTTP_200_OK,
    summary="Create a new project",
    description="Creates a project with a name and optional description. Returns the created project."
)
async def create_project_endpoint(
    project_in: schemas.ProjectCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
) -> schemas.ProjectRead:
    """
    Endpoint to create a new project. Returns 200 OK with the project data, or 400 on error.
    """
    new_project = await project.create_project(project_in, db)
    return new_project


@router.get(
    "/{project_id}/overview",
    response_model=schemas.ProjectOverview,
    summary="Get project overview",
    description="Returns project name, description, creation date components, and creation time."
)
async def project_overview_endpoint(
    project_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
) -> schemas.ProjectOverview:
    """
    Fetches overview information for a given project.
    """
    return await project.get_project_overview(project_id, db)


@router.put(
    "/{project_id}/update",
    response_model=schemas.ProjectRead,
    summary="Edit an existing project",
    description="Updates the project name and description for the specified project."
)
async def edit_project_endpoint(
    project_id: UUID,
    project_in: schemas.ProjectCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
) -> schemas.ProjectRead:
    """
    Endpoint to edit a project. Returns the updated project, or appropriate error codes.
    """
    updated = await project.edit_project(
        project_id=project_id,
        project_in=project_in,
        user_email=current_user.email,
        db=db
    )
    return updated

@router.get(
    "/projects",
    response_model=List[schemas.ProjectOverviewRead],
    summary="Get all projects for current user",
    description="Returns all projects created by the authenticated user."
)
async def get_all_projects(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
) -> List[schemas.ProjectOverviewRead]:
    """
    Endpoint to fetch all projects for the logged-in user.
    """
    return await project.get_all_projects_by_user(current_user.email, db)

@router.get(
    "/recent/7days",
    response_model=List[schemas.ProjectOverviewRead],
    summary="Get projects created in the last 7 days",
    description="Fetches all projects created by the authenticated user within the last seven days."
)
async def get_recent_7days_projects(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
) -> List[schemas.ProjectOverviewRead]:
    """
    Endpoint to fetch all projects the user created in the last 7 days.
    """
    return await project.get_projects_created_last_7_days(current_user.email, db)

@router.post("/{project_id}/events")
async def add_event_to_project(
    project_id: UUID,
    event_data: schemas.ProjectEventCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    """
    Endpoint to add an event to a project.
    This endpoint allows the user to create an event associated with a specific project.
    It requires the project ID and event data, and returns the created event.
    """
    # Validate that the project exists and belongs to the current user
    project_exists = await project.get_project_by_id(project_id, db)
    if not project_exists:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    if project_exists.user_email != current_user.email:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You do not have permission to add events to this project"
        )

    # Create the event for the project
    # This function will handle the creation logic and return the created event
    event = await project.create_event_for_project(
    project_id=project_id,
    user_id=current_user.user_id,
    event_data=event_data,
    db=db
)
