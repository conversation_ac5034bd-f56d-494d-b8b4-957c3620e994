// export default function SuccessDialog({ isOpen, onClose }) {
//   if (!isOpen) return null;

//   return (
//     <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
//       <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-sm text-center">
//         <h2 className="text-lg font-semibold mb-4">Project Created!</h2>
//         <p className="text-gray-500 mb-4">
//           Your project has been successfully created.
//         </p>
//         <button
//           onClick={onClose}
//           className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-opacity-80"
//         >
//           OK
//         </button>
//       </div>
//     </div>
//   );
// }
