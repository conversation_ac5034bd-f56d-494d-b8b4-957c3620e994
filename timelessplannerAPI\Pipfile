[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = {extras = ["standard"], version = "*"}
python-multipart = "*"
alembic = "*"
asyncpg = "*"
pyjwt = "*"
passlib = {extras = ["bcrypt"], version = "*"}
pydantic-settings = "*"
sqlalchemy = "*"
python-dotenv = "*"
psycopg2-binary = "*"
bcrypt = "<4.1.0"
boto3 = "*"

[dev-packages]

[requires]
python_version = "3.13"
