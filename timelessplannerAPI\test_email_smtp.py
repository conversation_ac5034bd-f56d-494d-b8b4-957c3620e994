import pytest
from unittest.mock import patch, MagicMock
from src.services.email_service_smtp import EmailService, SMTPMailer
import smtplib

pytest_plugins = ("pytest_asyncio",)


@pytest.fixture
def mock_smtp():
    with patch("smtplib.SMTP") as mock_smtp:
        yield mock_smtp


@pytest.mark.asyncio
async def test_send_email_success(mock_smtp):
    mock_server = MagicMock()
    mock_smtp.return_value.__enter__.return_value = mock_server

    await EmailService.send_email(
        subject="Test Subject",
        recipients="<EMAIL>",
        body="Hello World"
    )

    mock_server.starttls.assert_called_once()
    mock_server.login.assert_called_once()
    mock_server.sendmail.assert_called_once()


@pytest.mark.asyncio
async def test_send_email_multiple_recipients(mock_smtp):
    mock_server = MagicMock()
    mock_smtp.return_value.__enter__.return_value = mock_server

    await EmailService.send_email(
        subject="Test Multi",
        recipients=["<EMAIL>", "<EMAIL>"],
        body="Test body"
    )

    mock_server.sendmail.assert_called_once()
    args = mock_server.sendmail.call_args[0]
    assert "<EMAIL>" in args[1] and "<EMAIL>" in args[1]


@pytest.mark.asyncio
async def test_send_email_failure_with_retries(mock_smtp):
    mock_server = MagicMock()
    mock_smtp.return_value.__enter__.return_value = mock_server
    mock_server.sendmail.side_effect = smtplib.SMTPException("Send failed")

    with pytest.raises(smtplib.SMTPException):
        await EmailService.send_email(
            subject="Should Fail",
            recipients="<EMAIL>",
            body="This will fail",
            max_retries=2
        )

    assert mock_server.sendmail.call_count == 2


@pytest.mark.asyncio
async def test_send_invite_email(mock_smtp):
    mock_server = MagicMock()
    mock_smtp.return_value.__enter__.return_value = mock_server

    await EmailService.send_invite_email("<EMAIL>", "https://test-link.com")

    mock_server.sendmail.assert_called_once()
    args = mock_server.sendmail.call_args[0]
    assert "https://test-link.com" in args[2]


@pytest.mark.asyncio
async def test_send_reminder_email(mock_smtp):
    mock_server = MagicMock()
    mock_smtp.return_value.__enter__.return_value = mock_server

    await EmailService.send_reminder_email("Reminder!", "<EMAIL>", "Don't forget!")

    mock_server.sendmail.assert_called_once()


@pytest.mark.asyncio
async def test_send_otp_email(mock_smtp):
    mock_server = MagicMock()
    mock_smtp.return_value.__enter__.return_value = mock_server

    await EmailService.send_otp_email("<EMAIL>", "123456")

    mock_server.sendmail.assert_called_once()
    args = mock_server.sendmail.call_args[0]
    assert "123456" in args[2]
