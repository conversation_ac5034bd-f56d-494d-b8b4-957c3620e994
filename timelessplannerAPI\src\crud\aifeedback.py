from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def create_ai_form(data: schemas.AIFormCreate, db: AsyncSession):
    new_form = models.AIForm(
        ai_form_id=uuid4(),
        form_title=data.form_title,
        event_id=data.event_id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )
    db.add(new_form)
    await db.commit()
    await db.refresh(new_form)
    return {
        "message": "AI form created successfully",
        "ai_form_id": new_form.ai_form_id,
        "form_title": new_form.form_title
    }

async def get_ai_form_detail(ai_form_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(models.AIForm.form_title, models.Event.event_name)
        .join(models.Event, models.AIForm.event_id == models.Event.event_id)
        .where(models.AIForm.ai_form_id == ai_form_id)
    )
    form_detail = result.first()
    if not form_detail:
        return {"message": "AI Form not found"}
    
    return {
        "form_title": form_detail.form_title,
        "event_name": form_detail.event_name
    }


async def edit_ai_form_detail(ai_form_id: UUID, new_title: str, db: AsyncSession):
    result = await db.execute(select(models.AIForm).where(models.AIForm.ai_form_id == ai_form_id))
    ai_form = result.scalar_one_or_none()

    if ai_form is None:
        return {"message": "AI Form not found"}

    ai_form.form_title = new_title
    await db.commit()
    await db.refresh(ai_form)

    return {
        "message": "AI Form updated successfully",
        "form_title": ai_form.form_title
    }

async def create_feedback(ai_form_id: UUID, questions: List[dict], db: AsyncSession):
    feedback_entries = []

    for q in questions:
        feedback = models.FeedbackQuestion(
            ai_form_id=ai_form_id,
            question_initial=q["question_initial"],
            question=q["question"],
            created_at=datetime.utcnow()
        )
        db.add(feedback)
        feedback_entries.append(feedback)

    await db.commit()
    return {"message": "Feedback questions added successfully", "total": len(feedback_entries)}

async def customize_theme(
    ai_form_id: UUID,
    theme_data: dict,
    db: AsyncSession
):
    # Check if a theme already exists for this AI form
    result = await db.execute(select(models.AIFormTheme).where(models.AIFormTheme.ai_form_id == ai_form_id))
    existing_theme = result.scalar_one_or_none()

    if existing_theme:
        # Update existing theme
        for key, value in theme_data.items():
            setattr(existing_theme, key, value)
    else:
        # Create new theme
        new_theme = models.AIFormTheme(ai_form_id=ai_form_id, **theme_data)
        db.add(new_theme)

    await db.commit()
    return {"message": "Theme customization saved successfully"}

async def setup_ai_form_response_logic(
    ai_form_id: UUID,
    logic_data: dict,
    db: AsyncSession
):
    # Check if logic already exists
    result = await db.execute(
        select(models.AIFormResponseLogic).where(models.AIFormResponseLogic.ai_form_id == ai_form_id)
    )
    existing_logic = result.scalar_one_or_none()

    if existing_logic:
        for key, value in logic_data.items():
            setattr(existing_logic, key, value)
    else:
        logic = models.AIFormResponseLogic(ai_form_id=ai_form_id, **logic_data)
        db.add(logic)

    await db.commit()
    return {"message": "AI Form response logic configured successfully"}

async def get_share_ai_form(ai_form_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(models.AIForm).where(models.AIForm.ai_form_id == ai_form_id)
    )
    ai_form = result.scalar_one_or_none()

    if not ai_form:
        raise HTTPException(status_code=404, detail="AI Form not found")

    if not ai_form.share_link:
        # Optionally generate a share link if not present
        # Example link pattern: https://yourdomain.com/forms/<form_id>
        ai_form.share_link = f"https://yourdomain.com/forms/{ai_form_id}"
        db.add(ai_form)
        await db.commit()

    return {"share_link": ai_form.share_link}