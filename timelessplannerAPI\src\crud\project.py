
from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas


async def create_project(
    project_in: schemas.ProjectCreate,
    db: AsyncSession
) -> models.Project:
    """
    Creates a new Project record in the database.
    Raises HTTPException on integrity errors.
    """
    new_project = models.Project(
        project_name=project_in.project_name,
        project_description=project_in.project_description,
        user_email=project_in.user_email
    )
    db.add(new_project)
    try:
        await db.commit()
        await db.refresh(new_project)
    except IntegrityError as e:
        await db.rollback()
        # Could be foreign key failure or duplicate
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating project: {e.orig.args[0]}"
        )
    return new_project

async def get_project_overview(
    project_id: UUID,
    db: AsyncSession
) -> schemas.ProjectOverview:
    # Fetch the project
    stmt = select(models.Project).where(models.Project.project_id == project_id)
    result = await db.execute(stmt)
    project = result.scalar_one_or_none()

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project with id {project_id} not found"
        )

    # Count related events safely
    event_count_stmt = select(func.count()).select_from(models.Event).where(models.Event.project_id == project_id)
    event_count_result = await db.execute(event_count_stmt)
    event_count = event_count_result.scalar() or 0

    # Extract date and time components
    created_dt = project.created_at

    return schemas.ProjectOverview(
        project_name=project.project_name,
        project_description=project.project_description,
        created_day=created_dt.day,
        created_month=created_dt.month,
        created_year=created_dt.year,
        created_time=created_dt.time(),
        created_at=created_dt,
        event_count=event_count
    )

async def edit_project(
    project_id: UUID,
    project_in: schemas.ProjectCreate,
    user_email: str,
    db: AsyncSession
) -> models.Project:
    """
    Update an existing project's name and description. Ensures the project belongs to user_email.
    Raises HTTPException if not found or unauthorized.
    """
    # Fetch existing project
    result = await db.execute(select(models.Project).where(models.Project.project_id == project_id))
    project = result.scalar_one_or_none()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project with id {project_id} not found"
        )
    # Authorization check
    if project.user_email != user_email:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to edit this project"
        )
    # Update fields
    project.project_name = project_in.project_name
    project.project_description = project_in.project_description

    # Persist
    try:
        await db.commit()
        await db.refresh(project)
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    return project

async def get_all_projects_by_user(
    user_email: str,
    db: AsyncSession
) -> List[schemas.ProjectOverviewRead]:
    """
    Fetches all projects created by the specified user email,
    returning summary data including creation date breakdown and event count.
    """
    stmt = (
        select(models.Project)
        .options(selectinload(models.Project.events))
        .where(models.Project.user_email == user_email)
    )
    result = await db.execute(stmt)
    projects = result.scalars().all()

    return [
        schemas.ProjectOverviewRead(
            project_id=p.project_id,
            user_email=p.user_email,
            project_name=p.project_name,
            project_description=p.project_description,
            created_day=p.created_at.day,
            created_month=p.created_at.month,
            created_year=p.created_at.year,
            created_time=p.created_at.time(),
            created_at=p.created_at,
            event_count=len(p.events)
        )
        for p in projects
    ]

async def get_projects_created_last_7_days(
    user_email: str,
    db: AsyncSession
) -> List[schemas.ProjectOverviewRead]:
    """
    Returns all projects created by the user in the last 7 days,
    including creation date breakdown and event count.
    """
    one_week_ago = datetime.utcnow() - timedelta(days=7)

    stmt = (
        select(models.Project)
        .options(selectinload(models.Project.events))
        .where(
            models.Project.user_email == user_email,
            models.Project.created_at >= one_week_ago
        )
        .order_by(models.Project.created_at.desc())
    )

    result = await db.execute(stmt)
    projects = result.scalars().all()

    return [
        schemas.ProjectOverviewRead(
            project_id=p.project_id,
            user_email=p.user_email,
            project_name=p.project_name,
            project_description=p.project_description,
            created_day=p.created_at.day,
            created_month=p.created_at.month,
            created_year=p.created_at.year,
            created_time=p.created_at.time(),
            created_at=p.created_at,
            event_count=len(p.events)
        )
        for p in projects
    ]

async def create_event_for_project(
    project_id: UUID,
    user_id: UUID,
    event_data: schemas.ProjectEventCreate,
    db: AsyncSession
) -> models.Event:
    new_event = models.Event(
        event_name=event_data.event_name,
        team=event_data.team,
        project_id=project_id,
        user_id=user_id
    )
    db.add(new_event)
    await db.commit()
    await db.refresh(new_event)
    return new_event

async def get_project_by_id(
    project_id: UUID,
    db: AsyncSession
) -> models.Project:
    """
    Fetch a single project by its ID.
    Raises a 404 error if not found.
    """
    stmt = select(models.Project).where(models.Project.project_id == project_id)
    result = await db.execute(stmt)
    project = result.scalar_one_or_none()

    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project with id {project_id} not found"
        )

    return project