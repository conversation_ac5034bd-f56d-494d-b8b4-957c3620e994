from pydantic import  EmailStr
from src.utils.commonImports import *
from src.schemas import schemas
from src.models import models

async def create_user_otp(db: AsyncSession, 
    user_otp_create: schemas.UserOTPCreate
    ) -> models.UserOTP:
    # Create an instance of UserOTP from the schema
    user_otp = models.UserOTP(
        user_id=user_otp_create.user_id,
        email=user_otp_create.email,
        otp_code=user_otp_create.otp_code,
        is_used=user_otp_create.is_used
    )
    
    # Add to session and commit
    db.add(user_otp)
    await db.commit()
    
    # Refresh to get the ID assigned
    await db.refresh(user_otp)
    
    return user_otp

#get user otp by email
async def get_user_otp_by_email(
    db: AsyncSession,  
    email: EmailStr,  
    otp_code: str
) -> Optional[models.UserOTP]:
    # Query the OTPs sent to a specific user email (otp_type)
    result = await db.execute(
        select(models.UserOTP)
        .where(models.UserOTP.email == email)
        .where(models.UserOTP.otp_code == otp_code)
        .order_by(models.UserOTP.created_at.desc())
        .limit(1)
    )
    otp = result.scalars().first()
    return otp

#get user last sent otp
async def get_last_user_otp(
    db: AsyncSession, 
    user_id: UUID
    ) -> Optional[models.UserOTP]:
    """
    Retrieves the most recent OTP for a given user and OTP type.
    """
    logging.info(f"Fetching last OTP for user_id: {user_id}")

    result = await db.execute(
        select(models.UserOTP)
        .where(models.UserOTP.user_id == user_id)
        .order_by(models.UserOTP.created_at.desc()) 
        .limit(1) 
    )
    
    otp = result.scalar_one_or_none()
    
    if otp:
        logging.info(f"Found OTP: {otp.otp_code}")
    else:
        logging.info("No OTP found.")
    
    return otp

async def update_user_otp(
    db: AsyncSession, 
    otp_id: UUID, 
    user_otp_update: schemas.UserOTPUpdate
) -> Optional[models.UserOTP]:
    # Fetch the OTP
    result = await db.execute(select(models.UserOTP).where(models.UserOTP.otp_id == otp_id))
    otp = result.scalars().first()
    
    if otp is None:
        return None
    
    # Update fields if provided
    if user_otp_update.is_used is not None:
        otp.is_used = user_otp_update.is_used
    
    # Commit the changes
    try:
        await db.commit()
        await db.refresh(otp)
        return otp
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))



