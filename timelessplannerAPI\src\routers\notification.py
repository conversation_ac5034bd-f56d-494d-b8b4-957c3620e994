from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import notification


router = APIRouter(prefix="/notification", tags=["notifications"])

@router.get("/triggered/user", response_model=List[schemas.NotificationRead])
async def triggered_notifications_for_user(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await notification.get_triggered_notifications_for_user(current_user.user_id, db)


@router.post("/preferences", status_code=200)
async def set_notification_preferences_route(
    pref_data: NotificationPreferenceCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await notification.set_notification_preferences(
        user_id=current_user.user_id,
        pref_data=pref_data,
        db=db
    )