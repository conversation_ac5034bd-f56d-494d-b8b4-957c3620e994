# src/crud/reset_tokens.py
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from src.models import models
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import update
from fastapi import HTTPException
from sqlalchemy import select
from src.schemas import schemas

async def create_reset_token(db: AsyncSession, data: schemas.PasswordResetTokenCreate) -> models.PasswordResetToken:
    obj = models.PasswordResetToken(**data.model_dump())
    db.add(obj)
    await db.commit()
    await db.refresh(obj)
    return obj

async def get_valid_token(db: AsyncSession, token: str) -> models.PasswordResetToken | None:
    stmt = select(models.PasswordResetToken).where(
        models.PasswordResetToken.token == token,
        models.PasswordResetToken.expires_at > datetime.utcnow(),
        models.PasswordResetToken.used == False
    )
    res = await db.execute(stmt)
    return res.scalars().first()

async def mark_token_as_used(db: AsyncSession, token: str) -> None:
    stmt = select(models.PasswordResetToken).where(
        models.PasswordResetToken.token == token
    )
    res = await db.execute(stmt)
    token_obj = res.scalars().first()
    if token_obj:
        token_obj.used = True
        await db.commit()
        await db.refresh(token_obj)
    else:
        raise HTTPException(status_code=404, detail="Token not found")
 