from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas
from datetime import date

async def create_task(db: AsyncSession, task_data: schemas.CreateTaskRequest, user_id, event_id):
    new_task = models.Task(
        task_id=uuid4(),
        task_name=task_data.task_name,
        task_description=task_data.task_description,
        start_date=task_data.start_date,
        end_date=task_data.end_date,
        user_id=user_id,
        event_id=event_id
    )
    
    db.add(new_task)
    await db.commit()
    await db.refresh(new_task)
    
    return schemas.CreateTaskResponse(
        task_id=new_task.task_id,
        task_name=new_task.task_name,
        task_description=new_task.task_description,
        start_date=new_task.start_date,
        end_date=new_task.end_date
    )

async def add_task_detail(
    db: AsyncSession,
    task_id: UUID,
    detail_data: schemas.AddTaskDetailRequest
) -> schemas.AddTaskDetailResponse:
    result = await db.execute(select(models.Task).where(models.Task.task_id == task_id))
    task = result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found.")

    task.task_description = detail_data.task_description
    task.task_instruction = detail_data.task_instruction
    task.attachment_url = detail_data.attachment_url
    task.maximum_team = detail_data.maximum_team
    task.priority = detail_data.priority
    task.linked_to_event = detail_data.linked_to_event
    task.event_id = detail_data.event_id

    await db.commit()
    await db.refresh(task)

    return schemas.AddTaskDetailResponse(
        task_id=task.task_id,
        task_description=task.task_description,
        task_instruction=task.task_instruction,
        attachment_url=task.attachment_url,
        maximum_team=task.maximum_team,
        priority=task.priority,
        linked_to_event=task.linked_to_event,
        event_id=task.event_id
    )


async def attach_team_members_to_task(
    db: AsyncSession,
    task_id: UUID,
    request_data: schemas.AttachTeamMembersRequest,
) -> schemas.AttachTeamMembersResponse:
    # Normalize to a list
    members: List[schemas.TeamMemberAssignment] = (
        request_data.members if isinstance(request_data.members, list) else [request_data.members]
    )

    # Fetch task and its assigned teams
    task_result = await db.execute(select(models.Task).where(models.Task.task_id == task_id))
    task = task_result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found.")

    if not task.assigned_teams:
        raise HTTPException(status_code=400, detail="No team assigned to this task.")

    team = task.assigned_teams[0]  # Default: use first assigned team
    added_emails = []

    for member in members:
        # Check if member already exists in this team
        existing_query = await db.execute(
            select(models.TeamMember).where(
                models.TeamMember.email == member.email,
                models.TeamMember.team_id == team.team_id,
            )
        )
        existing_member = existing_query.scalars().first()
        if existing_member:
            continue  # Skip if already in the team

        new_member = models.TeamMember(
            email=member.email,
            role=member.role,
            member_name=member.email.split("@")[0].capitalize(),  # Default name from email
            team_id=team.team_id,
            user_id=UUID("00000000-0000-0000-0000-000000000000")  # Optional placeholder
        )
        db.add(new_member)
        added_emails.append(member.email)

    await db.commit()

    return schemas.AttachTeamMembersResponse(
        task_id=task.task_id,
        team_id=team.team_id,
        added_members=added_emails
    )

async def get_task_overview(db: AsyncSession, task_id: UUID) -> schemas.TaskOverviewResponse:
    # Load Task with related Event and ChatSessions
    result = await db.execute(
        select(models.Task)
        .options(
            joinedload(models.Task.event),
            joinedload(models.Task.chat_session).joinedload(models.ChatSession.chat_messages)
        )
        .where(models.Task.task_id == task_id)
    )
    task = result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found.")

    # Calculate days_left
    today = date.today()
    days_left = (task.end_date - today).days if task.end_date else None

    # Parse team_member_images (stored as comma-separated string)
    team_images = (
        task.team_member_images.split(",") if task.team_member_images else []
    )

    # Count total chat messages from all chat sessions
    total_chats = sum(len(session.chat_messages) for session in task.chat_session)

    return schemas.TaskOverviewResponse(
        task_id=task.task_id,
        task_name=task.task_name,
        event_name=task.event.event_name if task.event else "No Event",
        days_left=days_left,
        progress_value=task.progress_value,
        team_member_images=team_images,
        task_status=task.task_status,
        total_chats=total_chats,
    )

async def get_task_detail(db: AsyncSession, task_id: UUID) -> schemas.TaskDetailResponse:
    result = await db.execute(
        select(models.Task).where(models.Task.task_id == task_id)
    )
    task = result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found.")

    # Parse team images and limit to 5
    team_images = (
        task.team_member_images.split(",")[:5] if task.team_member_images else []
    )

    return schemas.TaskDetailResponse(
        task_id=task.task_id,
        task_name=task.task_name,
        priority=task.priority,
        start_date=task.start_date,
        end_date=task.end_date,
        team_member_images=team_images,
        team_name=task.team_name,
        task_status=task.task_status,
        team_lead_email=task.team_lead_email,
        task_description=task.task_description,
    )

async def get_task_progress(db: AsyncSession, task_id: UUID) -> schemas.TaskProgressResponse:
    result = await db.execute(select(models.Task).where(models.Task.task_id == task_id))
    task = result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found.")

    return schemas.TaskProgressResponse(
        task_id=task.task_id,
        progress_description=task.progress_description,
        progress_status=task.progress_status,
    )

async def get_task_team_chat(db: AsyncSession, task_id: UUID) -> schemas.TaskTeamChatResponse:
    # Fetch task
    result = await db.execute(select(models.Task).where(models.Task.task_id == task_id))
    task = result.scalars().first()
    if not task:
        raise HTTPException(status_code=404, detail="Task not found.")

    # Ensure there's at least one chat session for this task
    chat_session_result = await db.execute(
        select(models.ChatSession).where(models.ChatSession.task_id == task_id)
    )
    chat_session = chat_session_result.scalars().first()
    if not chat_session:
        raise HTTPException(status_code=404, detail="Chat session not found for task.")

    # Get team info
    team = chat_session.team
    if not team:
        raise HTTPException(status_code=404, detail="Team not found for chat session.")

    # Get team members (images)
    member_images = [member.image for member in team.members if member.image][:5]

    # Get chat messages
    chat_msg_result = await db.execute(
        select(models.ChatMessage).where(models.ChatMessage.chat_session_id == chat_session.chat_session_id).order_by(models.ChatMessage.created_at)
    )
    chat_msgs = chat_msg_result.scalars().all()

    message_list = []
    for msg in chat_msgs:
        sender = msg.sender
        formatted_timestamp = msg.created_at.strftime("%A %I:%M %p")  # Day of week, time (e.g., "Tuesday 03:15 PM")
        message_list.append(models.ChatMessageInfo(
            sender_name=sender.member_name,
            sender_image=sender.image,
            message=msg.message,
            attachment=msg.attachment,
            timestamp=formatted_timestamp
        ))

    return schemas.TaskTeamChatResponse(
        team_name=team.team_name,
        team_member_images=member_images,
        messages=message_list
    )

async def get_complete_task(db: AsyncSession, task_id: UUID) -> schemas.CompleteTaskResponse:
    result = await db.execute(select(models.Task).where(models.Task.task_id == task_id))
    task = result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    event = task.event
    if not event:
        raise HTTPException(status_code=404, detail="Event not found for this task")

    # Get the first chat session
    chat_sessions = task.chat_session
    total_chats = sum(len(session.chat_messages) for session in chat_sessions)

    # Get up to 2 team member images
    team_images = []
    if task.assigned_teams:
        team = task.assigned_teams[0]  # Assuming only one team is attached
        team_images = [member.image for member in team.members if member.image][:2]
        team_name = team.team_name
    else:
        team_name = None

    return schemas.CompleteTaskResponse(
        task_name=task.task_name,
        event_name=event.event_name,
        completion_percentage=task.completion_percentage,
        task_status=task.task_status,
        days_since_completion=task.days_since_completion,
        team_images=team_images,
        team_name=team_name,
        total_chats=total_chats
    )

async def get_list_task_team(db: AsyncSession, task_id: UUID) -> List[schemas.TaskTeamMemberResponse]:
    result = await db.execute(select(models.Task).where(models.Task.task_id == task_id))
    task = result.scalars().first()

    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    team_members = []
    for team in task.assigned_teams:
        for member in team.members:
            team_members.append(schemas.TaskTeamMemberResponse(
                full_name=member.member_name,
                email=member.email,
                role=member.role,
                status=task.task_status  # Reuse task status
            ))

    return team_members