from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import userprofile

router = APIRouter(prefix="/userprofile", tags=["userprofile"])

@router.get("/profile")
async def fetch_user_profile(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    profile = await userprofile.get_user_profile(current_user["user_id"], db)

    if not profile:
        raise HTTPException(status_code=404, detail="User profile not found")

    return {
        "user_image": profile.user_image,
        "full_name": profile.full_name,
        "email": profile.email,
        "phone_number": profile.phone_number,
        "job_title": profile.job_title,
        "total_events": profile.total_events,
        "total_projects": profile.total_projects,
        "total_tasks": profile.total_tasks
    }

@router.put("/edit-profile", status_code=status.HTTP_200_OK)
async def update_user_profile(
    payload: schemas.UserUpdate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await userprofile.edit_user_profile(db=db, user_id=current_user.user_id, payload=payload)

@router.post("/crm/integrate", status_code=status.HTTP_201_CREATED)
async def integrate_crm_route(
    payload: CRMIntegrationCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await userprofile.integrate_crm(db=db, payload=payload)

@router.get("/billing/user/{user_id}", response_model=schemas.BillingPlanResponse)
async def get_user_billing_route( 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await userprofile.get_user_billing_plan(db, current_user.user_id)

@router.put("/billing/cancel")
async def cancel_billing_plan_route(
    payload: schemas.CancelBillingRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await userprofile.cancel_billing_plan(db, payload.user_email, payload.plan_name)


@router.post("/billing/upgrade")
async def upgrade_plan_route(
    payload: schemas.UpgradePlanRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await userprofile.upgrade_plan(
        db,
        user_email=payload.user_email,
        current_plan=payload.current_plan,
        initial_plan=payload.initial_plan,
        initial_amount=payload.initial_amount,
        current_amount=payload.current_amount
    )

@router.post("/billing/add-payment-method")
async def add_payment_method_route(
    payload: schemas.AddPaymentMethodRequest,
    db: AsyncSession = Depends(get_db)
):
    return await userprofile.add_payment_method(
        db=db,
        user_email=payload.user_email,
        payment_method=payload.payment_method
    )