from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def create_workspace(data: shemas.WorkspaceCreateWithEmail, db: AsyncSession) -> models.Workspace:
    # Find user by email
    result = await db.execute(select(umodels.User).where(models.User.email == data.email))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User with this email does not exist."
        )

    new_workspace = models.Workspace(
        workspace_id=uuid4(),
        workspace_name=data.workspace_name,
        user_id=user.user_id,
        created_at=datetime.utcnow()
    )

    db.add(new_workspace)
    await db.commit()
    await db.refresh(new_workspace)

    return new_workspace