from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.models import models


router = APIRouter(prefix="/domain", tags=["domain"])

@router.post("/domain/add", response_model=schemas.DomainResponse)
def add_custom_domain(
    timeless_domain: bool = Body(...),
    company_name: str = Body(...),
    custom_domain: bool = Body(...),
    custom_domain_name: Optional[str] = Body(None),
    verification_status: str = Body(..., regex="^(verified|not verified)$"),
    type: Optional[str] = Body(None),
    name: Optional[str] = Body(None),
    value: Optional[str] = Body(None),
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    domain = models.Domain(
        timeless_domain=timeless_domain,
        company_name=company_name,
        custom_domain=custom_domain,
        custom_domain_name=custom_domain_name,
        verification_status=verification_status,
        type=type,
        name=name,
        value=value,
        user_id=current_user.user_id,
    )
    db.add(domain)
    db.commit()
    db.refresh(domain)
    return domain

@router.put("/domain/verify/{domain_id}", response_model=schemas.DomainResponse)
def verify_custom_domain(
    domain_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    domain = db.query(models.Domain).filter_by(domain_id=domain_id, user_id=current_user.user_id).first()
    if not domain:
        raise HTTPException(status_code=404, detail="Domain not found")
    
    if not domain.custom_domain_name or not domain.type or not domain.name or not domain.value:
        raise HTTPException(status_code=400, detail="Incomplete DNS records")

    # Simulate domain verification logic (replace with real DNS check logic)
    domain.verification_status = "verified"
    db.commit()
    db.refresh(domain)
    return domain