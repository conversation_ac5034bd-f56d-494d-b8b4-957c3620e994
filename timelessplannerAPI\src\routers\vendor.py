from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import vendor

router = APIRouter(prefix="/vendor", tags=["vendor"])

@router.get("/vendors/search", summary="Search for a vendor using Google Places API")
async def search_vendor_route(service_name: str = Query(..., description="Name of the vendor or service"),
                              location: Optional[str] = Query(None, description="Location to narrow down the search")):
    vendor_data = vendor.search_vendor(service_name, location)
    return vendor_data

@router.post("/vendors/add", response_model=schemas.VendorRead, status_code=status.HTTP_201_CREATED)
async def add_vendor_endpoint(
    vendor: schemas.VendorCreate,
    event_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    try:
        new_vendor = await vendor.add_vendor_manually(vendor, current_user.user_id, event_id, db)
        return new_vendor
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/vendors/view", response_model=List[schemas.VendorOut], summary="View all vendors")
async def view_all_vendors(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await vendor.view_vendors(db)

@router.post("/vendor/email", summary="Send email to a vendor")
async def send_vendor_email(request: EmailVendorRequest):
    try:
        return await vendor.email_vendor(vendor_email=request.vendor_email, message=request.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to send email: {str(e)}")

@router.post("/vendor/text", summary="Send SMS to a vendor")
async def send_sms_to_vendor(
    request: schemas.TextVendorRequest,
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await vendor.text_vendor(phone_number=request.phone_number, message=request.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to send SMS: {str(e)}")