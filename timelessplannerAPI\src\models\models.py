# model.py
from typing import Optional, List
from datetime import datetime, timezone, timedelta,date, time
from sqlalchemy import Date,Time
from uuid import UUID,uuid4
from enum import Enum

# SQLAlchemy specific imports
from sqlalchemy import String, Boolean, DateTime, ForeignKey,Text,Float, Integer,Table,Column,UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship, declarative_base



Base = declarative_base()

# Association table for Task-Team many-to-many relationship
task_teams = Table(
    'task_teams',
    Base.metadata,
    Column('task_id',ForeignKey('tasks.task_id'),primary_key=True),
    Column('team_id',ForeignKey('teams.team_id'),primary_key=True)
)

# Association table
event_team = Table(
    "event_team",
    Base.metadata,
    Column("event_id", ForeignKey("events.event_id"), primary_key=True),
    Column("team_id", ForeignKey("teams.team_id"), primary_key=True)
)

#Enum definition for deliveryMedium
class DeliveryMedium(str,Enum):
    SMS = 'sms'
    EMAIL = 'email'
    WHATSAPP = 'whatsapp'

def get_current_utc_time():
    return datetime.now(timezone.utc)


class User(Base):
    __tablename__ = "user"
    
    user_id: Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    first_name: Mapped[Optional[str]] = mapped_column(String(50))
    last_name: Mapped[Optional[str]] = mapped_column(String(50))
    job_title: Mapped[Optional[str]] = mapped_column(String(100))
    email: Mapped[str] = mapped_column(String(250), unique=True, index=True)
    phone_number: Mapped[Optional[str]] = mapped_column(String(16), unique=True, nullable=True)
    password: Mapped[Optional[str]] = mapped_column(String(128))
    is_active: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False,default=get_current_utc_time)
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), default=get_current_utc_time, nullable=False, onupdate=get_current_utc_time
    )


    # OAuth fields supporting both Google and Facebook authentication
    provider: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # e.g., "google" or "facebook"
    provider_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # OAuth provider's user ID
    user_image: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)  # Profile image URL

    projects: Mapped[List["Project"]] = relationship(back_populates="user")
    vendors: Mapped[List["Vendor"]] = relationship(back_populates="user")
    tasks: Mapped[List["Task"]] = relationship(back_populates="user")
    custom_domain: Mapped["Domain"] = relationship(back_populates="user")
    notification_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("notifications.notification_id"))
    notification: Mapped[Optional["Notification"]] = relationship(back_populates="users")
    notification_preference: Mapped["NotificationPreference"] = relationship(back_populates="user")
    workspaces: Mapped[List["Workspace"]] = relationship(back_populates="user")
    billing_plans = relationship("BillingPlan")
    crm_integrations: Mapped[List["CRMIntegration"]] = relationship(back_populates="user")
    reset_tokens: Mapped[List["PasswordResetToken"]] = relationship(
    back_populates="user")


    def __repr__(self):
        return f"<User(email={self.email}, name={self.first_name} {self.last_name})>"

class Token(Base):
    __tablename__ = "token"
    token_id: Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id",ondelete='CASCADE'), nullable=False)
    access_token: Mapped[str] = mapped_column(String(450), primary_key=True,unique=True)
    refresh_token: Mapped[str] = mapped_column(String(450), nullable=False,unique=True)
    status: Mapped[bool] = mapped_column(default=True)  # True means active, False means revoked
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=get_current_utc_time)
    revoked_at: Mapped[Optional[datetime]] = None  # Track when the token was revoked

class UserOTP(Base):
    __tablename__ = "user_otp"
    otp_id: Mapped[UUID] = mapped_column(default=uuid4,primary_key=True)
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id",ondelete='CASCADE'), nullable=False)
    email:Mapped[str] = mapped_column(String(250),index=True)
    otp_code: Mapped[str] = mapped_column(String(4), nullable=False) 
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=get_current_utc_time)
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc) + timedelta(minutes=30)
        )
    is_used: Mapped[bool] = mapped_column(Boolean, default=False)

class PasswordResetToken(Base):
    __tablename__ = "password_reset_tokens"

    password_reset_id:Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    user_id:Mapped[UUID] = mapped_column(ForeignKey("user.user_id", ondelete="CASCADE"), nullable=False)
    token:Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=get_current_utc_time)
    expires_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        default=lambda: datetime.now(timezone.utc) + timedelta(minutes=30)
        )
    used:Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # relationship back to User (if you have it)
    user = relationship("User", back_populates="reset_tokens")


class Project(Base):
    __tablename__ = "projects"
    
    project_id: Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    project_name: Mapped[str] = mapped_column(String(200), nullable=False)
    project_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # This field links the project to the user who created it.
    # The API expects an email to fetch user-specific projects.
    user_email: Mapped[str] = mapped_column(String(250), ForeignKey("user.email"), nullable=False)
    
    # Relationship to the User model (if needed in your application)
    user: Mapped[List["User"]] = relationship(back_populates="projects")
    events: Mapped[List["Event"]] = relationship(back_populates="project")

    def __repr__(self):
        return f"<Project(name={self.project_name}, created_at={self.created_at})>"


class Event(Base):
    __tablename__ = "events"
    
    # Primary Key
    event_id: Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    
    # Basic Event fields from addEvent endpoint
    event_name: Mapped[str] = mapped_column(String(200), nullable=False)
   
    
    # Associate with a Project (assuming a Project model exists with primary key project_id)
    project_id: Mapped[UUID] = mapped_column(ForeignKey("projects.project_id"), nullable=False)
    
    
    # Indicates whether this event is a team event
    team: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # Extended details from addEventDetail endpoint
    event_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    event_location_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # e.g., "in-person" or "virtual"
    event_location: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    street_address: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    state: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    state_initial: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    zip_code: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    country: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    time_zone: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    start_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    start_time: Mapped[Optional[time]] = mapped_column(Time, nullable=True)
    end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    end_time: Mapped[Optional[time]] = mapped_column(Time, nullable=True)
    logo: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    # Additional fields useful for event overviews
    industry: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Optional team details (for overviews such as team name and team lead email)
    team_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    team_lead_email: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    project: Mapped["Project"] = relationship(back_populates="events")
    guest_tags: Mapped[List["GuestTag"]] = relationship(back_populates="event")
    vendors: Mapped[List["Vendor"]] = relationship(back_populates="event")
    teams: Mapped[List["Team"]] = relationship(
        secondary=event_team, back_populates="events"
    )
    ai_forms: Mapped[List["AIForm"]] = relationship("AIForm", back_populates="event", foreign_keys="[AIForm.event_id]")
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id"))
    user: Mapped["User"] = relationship()
    tasks: Mapped[List["Task"]] = relationship(back_populates="event")

    def __repr__(self):
        return f"<Event(name={self.event_name}, project_id={self.project_id})>"


class GuestTag(Base):
    __tablename__ = "guest_tags"
    
    guest_tag_id: Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    # Name of the guest tag e.g., "VIP", "Family", etc.
    tag_name: Mapped[str] = mapped_column(String(100), nullable=False)
    
    # Associate the guest tag with a specific event.
    event_id: Mapped[UUID] = mapped_column(ForeignKey("events.event_id"), nullable=False)
    
    # Flags for additional functionality.
    rsvp_form: Mapped[bool] = mapped_column(Boolean, default=False)
    name_badge_printing: Mapped[bool] = mapped_column(Boolean, default=False)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship with the Event model (assuming Event model is defined elsewhere)
   
    event: Mapped["Event"] = relationship(back_populates="guest_tags")
    
    
    # One-to-many relationship with Guest
    guests: Mapped[List["Guest"]] = relationship(back_populates="guest_tag")
    rsvp_forms: Mapped[List["RSVPForm"]] = relationship(back_populates="guest_tag")
    broadcast_id: Mapped[UUID] = mapped_column(ForeignKey("broadcasts.broadcast_id"))
    broadcast: Mapped["Broadcast"] = relationship(back_populates="guest_tag")
    
    def __repr__(self):
        return f"<GuestTag(tag_name={self.tag_name}, event_id={self.event_id})>"

class Guest(Base):
    __tablename__ = "guests"
    
    guest_id: Mapped[UUID] = mapped_column(default=uuid4, primary_key=True)
    
    # Basic guest details.
    salutation: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)  # e.g., "Mr", "Ms"
    first_name: Mapped[str] = mapped_column(String(100), nullable=False)
    last_name: Mapped[str] = mapped_column(String(100), nullable=False)
    email: Mapped[str] = mapped_column(String(250), nullable=False)
    phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    
    # Additional guest-specific information.
    seating_style: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    external_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    affiliation: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    secondary_email: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    note: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # RSVP and check-in status for the guest.
    rsvp: Mapped[bool] = mapped_column(Boolean, default=False)
    checked_in: Mapped[bool] = mapped_column(Boolean, default=False)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Optional association with a GuestTag.
    guest_tag_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("guest_tags.guest_tag_id"), nullable=True)
    guest_tag: Mapped["GuestTag"] = relationship(back_populates="guests")
    rsvp_form_id: Mapped[UUID] = mapped_column(ForeignKey("rsvp_forms.rsvp_form_id"))
    rsvp_form: Mapped["RSVPForm"] = relationship(back_populates="guest")
    ai_form_id: Mapped[UUID] = mapped_column(ForeignKey("ai_forms.ai_form_id"))
    notification_settings: Mapped[list["NotificationSetting"]] = relationship(
    "NotificationSetting", back_populates="guest", cascade="all, delete-orphan"
)
    
    def __repr__(self):
        return f"<Guest(name={self.first_name} {self.last_name}, email={self.email})>"

class NotificationSetting(Base):
    __tablename__ = "notification_settings"

    notification_settings_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    email_recipients: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    text_recipients: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)

    guest_id: Mapped[UUID] = mapped_column(ForeignKey("guests.guest_id"), nullable=False)
    guest = relationship("Guest", back_populates="notification_settings")

    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

class Vendor(Base):
    __tablename__ = "vendors"
    
    vendor_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    
    # This field represents the vendor's name or service name as referenced in the endpoints.
    service_name: Mapped[str] = mapped_column(String(200), nullable=False)
    
    # Contact details
    email: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    phone_number: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    
    # Address details
    office_address: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    # Rating details as per searchVendor and viewVendors endpoints:
    # star_rating represents the rating (typically between 1-5)
    star_rating: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    # total_ratings captures the total number of ratings (or "star total value")
    total_ratings: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # For searchVendor endpoint: represents the last time the vendor was "opened" or accessed.
    last_opened: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id"))
    user: Mapped["User"] = relationship(back_populates="vendors")
    event_id: Mapped[UUID] = mapped_column(ForeignKey("events.event_id"))
    event: Mapped["Event"] = relationship(back_populates="vendors")
    
    def __repr__(self):
        return f"<Vendor(service_name={self.service_name}, star_rating={self.star_rating})>"


class Task(Base):
    __tablename__ = "tasks"
    
    # Unique identifier for each task.
    task_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    
    # Basic task details (from createTask endpoint)
    task_name: Mapped[str] = mapped_column(String(200), nullable=False)
    task_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Dates for the task
    start_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    end_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    
    # Extended task details (from addTaskDetail endpoint)
    task_instruction: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    attachment_url: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)  # URL to uploaded attachment
    maximum_team: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    priority: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Linking task to an event (if applicable)
    linked_to_event: Mapped[bool] = mapped_column(Boolean, default=False)
    
    
    # Overview fields (from getTaskOverview and getTaskDetail endpoints)
    days_left: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    progress_value: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    task_status: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    total_chats: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Detail fields from task overview
    team_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    team_lead_email: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    # Storing team member images as a comma-separated list (adjust storage as needed)
    team_member_images: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Progress fields (from getTaskProgress)
    progress_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    progress_status: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Completion details (from getCompleteTask endpoint)
    completion_percentage: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    days_since_completion: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Many to One Relationship to Event model 
    event_id: Mapped[UUID] = mapped_column(ForeignKey("events.event_id"))
    event: Mapped["Event"] = relationship(back_populates="tasks")
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id"))
    user: Mapped["User"] = relationship(back_populates="tasks")
    chat_session: Mapped[List["ChatSession"]] = relationship(back_populates="task")
    assigned_teams = relationship('Team', secondary=task_teams, back_populates='tasks')


    def __repr__(self):
        return f"<Task(name={self.task_name}, status={self.task_status})>"

class Team(Base):
    __tablename__ = "teams"
    
    team_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    team_name: Mapped[str] = mapped_column(String(100), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship to team members
    members = relationship("TeamMember", back_populates="team", cascade="all, delete-orphan")
    tasks = relationship('Task', secondary=task_teams, back_populates='assigned_teams')
    chat_sessions = relationship('ChatSession', back_populates='team')
    events: Mapped[List["Event"]] = relationship(
        secondary=event_team, back_populates="teams"
    )
    def __repr__(self):
        return f"<Team(name={self.team_name})>"

class TeamMember(Base):
    __tablename__ = "team_members"
     
    member_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    member_name: Mapped[str] = mapped_column(String(100), nullable=False)
    email: Mapped[str] = mapped_column(String(250), nullable=False)
    role: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    # Additional details for team overview endpoints
    image: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    skill: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    date_added: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship back to the Team model
    team = relationship("Team", back_populates="members")
    team_id: Mapped[UUID] = mapped_column(ForeignKey("teams.team_id"), nullable=False)
    user_id: Mapped[UUID] = mapped_column(ForeignKey('user.user_id'),nullable=False)  
    chat_messages: Mapped[List["ChatMessage"]] = relationship("ChatMessage", back_populates="sender")

    
    def __repr__(self):
        return f"<TeamMember(name={self.member_name}, role={self.role})>"


class RSVPForm(Base):
    __tablename__ = "rsvp_forms"
    
    rsvp_form_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    # Link to the GuestTag to which this RSVP form applies.
    guest_tag_id: Mapped[UUID] = mapped_column(ForeignKey("guest_tags.guest_tag_id"), nullable=False)
    
    # RSVP configuration fields as defined in the API documentation.
    rsvp_title: Mapped[str] = mapped_column(String(200), nullable=False)
    rsvp_sub_message: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    event_logo: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)  # Can serve as event awareness/logo
    button_type: Mapped[str] = mapped_column(String(50), nullable=False)
    sender_email: Mapped[str] = mapped_column(String(250), nullable=False)
    sender_reply_email: Mapped[str] = mapped_column(String(250), nullable=False)
    responsive: Mapped[bool] = mapped_column(Boolean, default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship to the GuestTag model (assumes a corresponding back_populates in GuestTag)
    guest_tag = relationship("GuestTag", back_populates="rsvp_forms", uselist=False)
    guest: Mapped[List["Guest"]] = relationship(back_populates="rsvp_form")
    
    def __repr__(self):
        return f"<RSVPForm(title={self.rsvp_title}, guest_tag_id={self.guest_tag_id})>"
    

class ChatSession(Base):
    __tablename__ = "chat_sessions"
    
    chat_session_id: Mapped[UUID] = mapped_column( primary_key=True, default=uuid4)
    session_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship to messages in this session
    chat_messages = relationship("ChatMessage", back_populates="chat_session")
    task_id: Mapped[UUID] = mapped_column(ForeignKey("tasks.task_id"), nullable=False)
    task: Mapped["Task"] = relationship(back_populates="chat_session")
    chat_messages: Mapped[List["ChatMessage"]] = relationship(back_populates="chat_session")
    team = relationship('Team', back_populates='chat_sessions')
  
    team_id: Mapped[UUID] = mapped_column(ForeignKey('teams.team_id'))
    __table_args__ = (UniqueConstraint('task_id', 'team_id', name='_task_team_uc'),)
    
    def __repr__(self):
        return f"<ChatSession(chat_session_id={self.chat_session_id}, task_id={self.task_id})>"

# Updated ChatMessage model that references a chat session instead of a task directly
class ChatMessage(Base):
    __tablename__ = "chat_messages"
    
    message_id: Mapped[UUID] = mapped_column( primary_key=True, default=uuid4)
    # Associate the message with a chat session instead of directly with a task
    chat_session_id: Mapped[UUID] = mapped_column(ForeignKey("chat_sessions.chat_session_id"), nullable=False)
    
    
    message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    attachment: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    media: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationships for easy access to the chat session and sender details.
    chat_session = relationship("ChatSession", back_populates="chat_messages")
    chat_session: Mapped["ChatSession"] = relationship(back_populates="chat_messages")
    sender_id: Mapped[UUID] = mapped_column(ForeignKey("team_members.member_id"), nullable=False)
    sender = relationship("TeamMember", back_populates="chat_messages")

    def __repr__(self):
        return (f"<ChatMessage(session_id={self.session_id}, sender_id={self.sender_id}, "
                f"created_at={self.created_at})>")

class Broadcast(Base):
    __tablename__ = "broadcasts"
    
    broadcast_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    
    # Associate the broadcast with a specific event (assumes an Event model exists)
    event_id: Mapped[UUID] = mapped_column(ForeignKey("events.event_id"), nullable=False)
    
    message_subject: Mapped[str] = mapped_column(String(200), nullable=False)
    message: Mapped[str] = mapped_column(Text, nullable=False)
    # The medium used for broadcasting (e.g., 'text', 'email', or 'whatsapp')
    medium: Mapped[str] = mapped_column(String(50), nullable=False)
    
    # You can add a status field (e.g., "sent" or "not-sent") if you need to track the broadcast delivery state.
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="not-sent")
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship to GuestTag, which in turn links to its guests.
    guest_tag: Mapped[List["GuestTag"]] = relationship(back_populates="broadcast")
    
    def __repr__(self):
        return f"<Broadcast(event_id={self.event_id}, guest_tag_id={self.guest_tag_id}, subject={self.message_subject})>"
    

class AIForm(Base):
    __tablename__ = "ai_forms"
    
    ai_form_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    form_title: Mapped[str] = mapped_column(String(200), nullable=False)
    # Optional shareable link for the form (provided by getShareAIForm endpoint)
    share_link: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships to related AI Feedback aspects
    feedback_questions: Mapped[List["FeedbackQuestion"]] = relationship(
        "FeedbackQuestion", back_populates="ai_form", cascade="all, delete-orphan"
    )
    theme: Mapped[Optional["AIFormTheme"]] = relationship(
        "AIFormTheme", uselist=False, back_populates="ai_form", cascade="all, delete-orphan"
    )
    response_logic: Mapped[Optional["AIFormResponseLogic"]] = relationship(
        "AIFormResponseLogic", uselist=False, back_populates="ai_form", cascade="all, delete-orphan"
    )
    guest: Mapped[List["Guest"]] = relationship()
    event_id: Mapped[UUID] = mapped_column(ForeignKey("events.event_id"), nullable=False)
    event: Mapped["Event"] = relationship("Event", back_populates="ai_forms", foreign_keys=[event_id])
    notification_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("notifications.notification_id"))
    notification: Mapped[Optional["Notification"]] = relationship(back_populates="ai_form")
    
    def __repr__(self):
        return f"<AIForm(title={self.form_title}, event_id={self.event_id})>"

class FeedbackQuestion(Base):
    __tablename__ = "feedback_questions"
    
    feedback_question_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    ai_form_id: Mapped[UUID] = mapped_column(ForeignKey("ai_forms.ai_form_id"), nullable=False)
    
    question_initial: Mapped[str] = mapped_column(String(100), nullable=False)
    question: Mapped[str] = mapped_column(Text, nullable=False)
    
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationship back to the AIForm
    ai_form = relationship("AIForm", back_populates="feedback_questions")
    
    def __repr__(self):
        return f"<FeedbackQuestion(initial={self.question_initial})>"

class AIFormTheme(Base):
    __tablename__ = "ai_form_themes"
    
    theme_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    ai_form_id: Mapped[UUID] = mapped_column(ForeignKey("ai_forms.ai_form_id"), nullable=False)
    
    theme_color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    brand_logo: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    background_image: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    layout_color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    text_color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    button_color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    button_text_color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    background_color: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    type_face: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    text_animation: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    button_radius: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    background_opacity: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    # Additional logo field if needed
    logo: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    # Relationship back to the AIForm
    ai_form = relationship("AIForm", back_populates="theme")
    
    def __repr__(self):
        return f"<AIFormTheme(ai_form_id={self.ai_form_id})>"

class AIFormResponseLogic(Base):
    __tablename__ = "ai_form_response_logics"
    
    response_logic_id: Mapped[UUID] = mapped_column( primary_key=True, default=uuid4)
    ai_form_id: Mapped[UUID] = mapped_column(ForeignKey("ai_forms.ai_form_id"), nullable=False)
    
    email_logic: Mapped[bool] = mapped_column(Boolean, default=False)
    email_subject: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    sender_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    sender_email: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    reply_email: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    # Relationship back to the AIForm
    ai_form = relationship("AIForm", back_populates="response_logic")
    
    def __repr__(self):
        return f"<AIFormResponseLogic(ai_form_id={self.ai_form_id}, email_logic={self.email_logic})>"


class Domain(Base):
    __tablename__ = "domains"

    domain_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    
    # Indicates if this is a default "timeless" domain provided by the system.
    timeless_domain: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # The company name associated with the domain.
    company_name: Mapped[str] = mapped_column(String(250), nullable=False)
    
    # Indicates whether a custom domain is being used.
    custom_domain: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # The custom domain name provided by the user.
    custom_domain_name: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    
    # Verification status of the domain. Expected values: "verified" or "not verified".
    verification_status: Mapped[str] = mapped_column(String(50), nullable=False, default="not verified")
    
    # Additional DNS record details that might be required for domain configuration.
    type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    name: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    value: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    # One -to- one relationship with user
    user_id: Mapped[int] = mapped_column(ForeignKey("user.user_id"))
    user: Mapped["User"] = relationship(back_populates="custom_domain")

    def __repr__(self):
        return (f"<Domain(company_name={self.company_name}, custom_domain={self.custom_domain}, "
                f"custom_domain_name={self.custom_domain_name}, verification_status={self.verification_status})>")
    

class Notification(Base):
    __tablename__ = "notifications"
    
    notification_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    # Captures the date (or month) when the notification was triggered.
    triggered_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    # Message heading as defined in getTriggeredNotification endpoint.
    message_heading: Mapped[str] = mapped_column(String(250), nullable=False)
    # Message body containing the detailed notification message.
    message_body: Mapped[str] = mapped_column(Text, nullable=False)
    users: Mapped[List["User"]] = relationship(back_populates="notification")
    ai_form: Mapped[List["AIForm"]] = relationship(back_populates="notification")
    
    def __repr__(self):
        return f"<Notification(triggered_at={self.triggered_at}, heading={self.message_heading})>"

class NotificationPreference(Base):
    __tablename__ = "notification_preferences"
    
    preference_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
   
    general_notification: Mapped[bool] = mapped_column(Boolean, default=True)
    task_completion: Mapped[bool] = mapped_column(Boolean, default=True)
    event_creation_and_completion: Mapped[bool] = mapped_column(Boolean, default=True)
    system_alert: Mapped[bool] = mapped_column(Boolean, default=True)
    
    # Notification method: expected values include 'email', 'sms', or 'push notification'.
    notification_method: Mapped[str] = mapped_column(String(50), nullable=False, default="email")
    # Frequency: expected values include 'instant', 'daily', or 'weekly'.
    frequency: Mapped[str] = mapped_column(String(50), nullable=False, default="instant")
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id"), nullable=False)
    user: Mapped["User"] = relationship(back_populates="notification_preference")
    
    def __repr__(self):
        return (f"<NotificationPreference(user_id={self.user_id}, "
                f"method={self.notification_method}, frequency={self.frequency})>")
    

class Workspace(Base):
    __tablename__ = "workspaces"
    
    workspace_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    workspace_name: Mapped[str] = mapped_column(String(200), nullable=False)
    email: Mapped[str] = mapped_column(String(250), unique=True, index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Many to One Relationship to the User model.
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id"))
    user: Mapped["User"] = relationship(back_populates="workspaces")

class BillingPlan(Base):
    __tablename__ = "billing_plans"
    
    billing_plan_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    plan_name: Mapped[str] = mapped_column(String(100), nullable=False)
    amount_paid: Mapped[float] = mapped_column(Float, nullable=False)
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="active")  # e.g., "active" or "canceled"
    initial_plan: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    payment_method: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # e.g., "Visa", "Mastercard"
    billing_contact: Mapped[Optional[str]] = mapped_column(String(250), nullable=True)
    billing_email: Mapped[str] = mapped_column(String(250), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id: Mapped[UUID] = mapped_column(ForeignKey("user.user_id"))

class CRMIntegration(Base):
    __tablename__ = "crm_integrations"
    
    crm_integration_id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    provider_name: Mapped[str] = mapped_column(String(100), nullable=False)
    url_link: Mapped[str] = mapped_column(String(250), nullable=False)
    integrated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Optional: Many to One Relationship to the User model.
    user_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("user.user_id"))
    user: Mapped[Optional["User"]] = relationship(back_populates="crm_integrations")
    
    def __repr__(self):
        return f"<CRMIntegration(user_email={self.user_email}, provider={self.provider_name})>"