from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import rsvp

router = APIRouter(prefix="/rsvp", tags=["rsvp"])

@router.get("/rsvp/guest-tags", response_model=List[schemas.GuestTagRSVPStatus])
async def get_all_guest_tags_with_rsvp_status(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await rsvp.get_all_guest_tags_with_rsvp_true(db)

@router.post("/rsvp/configure", response_model=None, status_code=201)
async def configure_rsvp_form(
    payload: schemas.RSVPFormCreate, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    await rsvp.configure_guest_tag_rsvp_form(payload, db)
    return {"message": "RSVP Form configured successfully"}

@router.get("/rsvp/configured", response_model=List[schemas.ConfiguredRSVPFormOut])
async def get_configured_rsvp_forms(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await rsvp.get_all_configured_guest_rsvp_forms(current_user.user_id, db)