from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import broadcast

router = APIRouter(prefix="/broadcast", tags=["broadcast"])

@router.get("/broadcast/guest-tags", response_model=List[schemas.GuestTagBroadcastOut])
async def list_guest_tags_with_broadcasts(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await broadcast.get_list_of_guest_tags(db)

@router.post("/broadcast/setup")
async def create_broadcast(
    data: schemas.BroadcastCreate, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await broadcast.setup_broadcast(data, db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))