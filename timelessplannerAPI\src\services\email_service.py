import os
import time
import logging
import functools
from typing import List, Union, Optional
import boto3
from botocore.exceptions import ClientError
from src.utils.config import settings
from src.utils.commonImports import *


logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def stub_send_email(subject, recipients, body=None, html_body=None, max_retries=3):
    print("📧 Stub Email Sent")
    print("To:", recipients)
    print("Subject:", subject)
    print("Body:", body)
    print("HTML Body:", html_body)
    return {'MessageId': 'stub-id', 'Response': 'Stub email sent successfully'}

def switch_mailer(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        is_dev = settings.ENVIRONMENT.lower() in ["dev", "local", "development"]
        if is_dev:
            return stub_send_email(*args, **kwargs)
        else:
            return await func(*args, **kwargs)
    return wrapper

class EmailService:
    ses_client = boto3.client('ses', 
                              region_name=settings.AMAZON_REGION,
                              aws_access_key_id=settings.AMAZON_ACCESS_KEY,
                              aws_secret_access_key=settings.AMAZON_SECRET_ACCESS_KEY)
    
    @staticmethod
    @switch_mailer
    async def send_email(subject: str, recipients: Union[str, List[str]], body: Optional[str] = None, html_body: Optional[str] = None, max_retries=3):
        if not body and not html_body:
            raise ValueError("Either 'html_body' or 'body' must be provided")

        if isinstance(recipients, str):
            recipients = [recipients]
        elif not isinstance(recipients, list):
            raise TypeError("Recipients must be a string or a list of strings")

        for attempt in range(max_retries):
            try:
                message_body = {}
                if body:
                    message_body['Text'] = {
                        'Data': body,
                        'Charset': 'UTF-8'
                        }
                if html_body:
                    message_body['Html'] = {
                        'Data': html_body,
                        'Charset': 'UTF-8'
                        }

                response = EmailService.ses_client.send_email(
                    Source=settings.MAIL_FROM,
                    Destination={
                        'ToAddresses': recipients
                        },
                        Message={
                            'Subject': {
                            'Data': subject,
                            'Charset': 'UTF-8'
                            },
                            'Body': message_body
                       }
                )
                logger.info(f"Email sent successfully. Message ID: {response['MessageId']}")
                return
            except ClientError as e:
                logger.error(f"Attempt {attempt + 1} - Failed to send email: {e.response['Error']['Message']}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    raise

    @staticmethod
    async def send_invite_email(recipients: str, invite_link: str):
        subject = "You Are Invited!"
        body = f"Please use the following link to join: {invite_link}"
        return await EmailService.send_email(subject, recipients, body)

    @staticmethod
    async def send_reminder_email(subject: str, recipients: str, reminder_message: str):
        body = f"{reminder_message}"
        return await EmailService.send_email(subject, recipients, body)

    @staticmethod
    async def send_otp_email(recipients: str, otp_code: str):
        subject = "Your OTP Code"
        body = f"Your OTP code is {otp_code}. Please use this to complete your verification on timelessplanner.com."
        return await EmailService.send_email(subject, recipients, body)


    @staticmethod
    async def send_password_reset_email(recipients: str, reset_link: str):
        if not reset_link:
            raise ValueError("Reset link must be provided")
        if not recipients:
            raise ValueError("Recipient email must be provided")
        if not isinstance(recipients, str):
            raise TypeError("Recipient must be a string")
        subject = "Reset your timelessplanner.com password"
        body = (
            f"You (or someone else) requested a password reset.\n\n"
            f"Click here to reset your password:\n{reset_link}\n\n"
            f"If you didn’t request this, you can safely ignore this email."
        )
        return await EmailService.send_email(subject, recipients, body)
