
from src.utils import config
import secrets
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from datetime import datetime, timedelta
from httpx import AsyncClient
from src.utils.config import settings
from src.utils.commonImports import Response, HTTPException
import jwt

# Add Google OAuth class
class GoogleOAuth:
    @staticmethod
    def create_oauth_state(response: Response) -> str:
        """Generate and set secure state cookie"""
        state = secrets.token_urlsafe(32)
        response.set_cookie(
            "oauth_state",
            state,
            httponly=True,
            secure=config.settings.ENV == "production",
            samesite="lax",
            max_age=300  # 5 minutes
            )
        return state

    @staticmethod
    def create_secure_jwt(user_id: str) -> str:
        """Generate JWT with security claims"""
        return jwt.encode(
            {
            "sub": user_id,
            "exp": datetime.utcnow() + timedelta(minutes=config.settings.ACCESS_TOKEN_EXPIRE_MINUTES),
            "iss": config.settings.JWT_ISSUER,
            "aud": config.settings.JWT_AUDIENCE,
            "scope": "openid email profile"
            },
        config.settings.JWT_SECRET_KEY,
        algorithm=config.settings.JWT_ALGORITHM
    )

    @staticmethod
    async def get_oauth_config():
        return {
            "client_id": settings.GOOGLE_CLIENT_ID,
            "client_secret": settings.GOOGLE_CLIENT_SECRET,
            "authorize_url": settings.GOOGLE_AUTHORIZE_URL,
            "token_url": settings.GOOGLE_TOKEN_URL,
            "userinfo_url": settings.GOOGLE_USERINFO_URL,
            "redirect_uri": settings.GOOGLE_REDIRECT_URL,
            "scopes": ["openid", "email", "profile"]
        }

    @staticmethod
    async def get_authorization_url():
        config = await GoogleOAuth.get_oauth_config()
        return (
            f"{config['authorize_url']}?"
            f"client_id={config['client_id']}&"
            f"redirect_uri={config['redirect_uri']}&"
            f"scope={','.join(config['scopes'])}&"
            f"response_type=code"
        )

    @staticmethod
    async def exchange_code(code: str):
        config = await GoogleOAuth.get_oauth_config()
        async with AsyncClient() as client:
            # Exchange code for tokens
            token_response = await client.post(
                config["token_url"],
                data={
                    "code": code,
                    "client_id": config["client_id"],
                    "client_secret": config["client_secret"],
                    "redirect_uri": config["redirect_uri"],
                    "grant_type": "authorization_code",
                },
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )
            if token_response.status_code != 200:
                raise HTTPException(status_code=400, detail="Invalid OAuth code")
            
            tokens = token_response.json()
            
            # Get user info with required fields
            user_response = await client.get(
                config["userinfo_url"],
                headers={"Authorization": f"Bearer {tokens['access_token']}"},
                timeout=10.0
            )
            if user_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to fetch user info from Google"
            )
            return user_response.json()


# Add Facebook OAuth class
class FacebookOAuth:
    @staticmethod
    async def get_oauth_config():
        return {
            "client_id": config.settings.FACEBOOK_CLIENT_ID,
            "client_secret": config.settings.FACEBOOK_CLIENT_SECRET,
            "authorize_url": config.settings.FACEBOOK_AUTHORIZE_URL,
            "token_url": config.settings.FACEBOOK_TOKEN_URL,
            "userinfo_url": config.settings.FACEBOOK_USERINFO_URL,
            "redirect_uri": config.settings.FACEBOOK_REDIRECT_URL,
            "scopes": ["email", "public_profile"]
        }

    @staticmethod
    async def exchange_code(code: str) -> dict:
        """Exchange authorization code for user data with validation"""
        config = FacebookOAuth.get_oauth_config()
        async with AsyncClient() as client:
            # Get access token
            token_response = await client.get(
                config["token_url"],
                params={
                    "client_id": config["client_id"],
                    "client_secret": config["client_secret"],
                    "redirect_uri": config["redirect_uri"],
                    "code": code,
                    "grant_type": "authorization_code"
                },
                timeout=10.0
            )
            
            if token_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Facebook token exchange failed"
                )
                
            tokens = token_response.json()
            
            # Validate token
            debug_response = await client.get(
                "https://graph.facebook.com/debug_token",
                params={
                    "input_token": tokens["access_token"],
                    "access_token": f"{config['client_id']}|{config['client_secret']}"
                }
            )
            
            debug_data = debug_response.json()
            if not debug_data.get("data", {}).get("is_valid", False):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid Facebook token"
                )
            
            # Get user info
            user_response = await client.get(
                config["userinfo_url"],
                params={
                    "fields": "id,name,email,picture",
                    "access_token": tokens["access_token"]
                },
                timeout=10.0
            )
            
            if user_response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to fetch Facebook user info"
                )
                
            return user_response.json()