import httpx
from src.utils.config import settings

class EbulkSMSService:
    def __init__(self):
        # EbulkSMS username and API key from configuration
        self.username = settings.EBULKSMS_USERNAME
        self.api_key = settings.EBULKSMS_API_KEY
        self.api_url = "https://api.ebulksms.com/sendwhatsapp.json"

    async def send_sms(self, recipient: str, message: str):
        """
        Sends an SMS to the recipient using EbulkSMS API.
        
        :param recipient: The recipient's phone number in international format (e.g., '2348012345678').
        :param message: The message to be sent.
        :return: Success or failure status.
        """
        # Prepare the payload for EbulkSMS API
        payload = {
            "SMS": {
                "auth": {
                    "username": self.username,  # EbulkSMS Username
                    "apikey": self.api_key  # API key for authentication
                },
                "message": {
                    "sender": "T-planner",  # Sender ID (max 11 characters)
                    "messagetext": message,  # SMS message content
                    "flash": "0"  # Normal SMS
                },
                "recipients": {
                    "gsm": [
                        {"msidn": recipient, "msgid": "uniqueid1"}  # Unique message ID for tracking
                    ]
                },
                "dndsender": 1  # Allow messages to DND numbers
            }
        }

        headers = {
            "Content-Type": "application/json"
        }

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(self.api_url, json=payload, headers=headers)
                response.raise_for_status()  # Raise an error for bad status codes
                return {"status": "success", "message": "SMS sent successfully.", "response": response.json()}
        except httpx.HTTPStatusError as e:
            return {"status": "failed", "message": f"HTTP error: {str(e)}"}
        except Exception as e:
            return {"status": "failed", "message": f"An error occurred: {str(e)}"}

    async def send_otp(self, recipient: str, otp_code: str):
        """
        Sends an OTP via SMS to the recipient.
        
        :param recipient: The recipient's phone number.
        :param otp_code: The OTP code to send.
        :return: Success or failure status.
        """
        message = f"Your OTP code is {otp_code}. Please use this code to complete your verification."
        return await self.send_sms(recipient, message)

    async def send_invite(self, recipient: str, invite_link: str):
        """
        Sends an invite link via SMS to the recipient.

        :param recipient: The recipient's phone number.
        :param invite_link: The invite link to send.
        :return: Success or failure status.
        """
        message = f"You are invited! Please use the following link to join: {invite_link}"
        return await self.send_sms(recipient, message)

    async def send_notification(self, recipient: str, notification_message: str):
        """
        Sends a notification message via SMS to the recipient.

        :param recipient: The recipient's phone number.
        :param notification_message: The notification message to send.
        :return: Success or failure status.
        """
        message = f"Notification: {notification_message}"
        return await self.send_sms(recipient, message)

    async def send_custom_message(self, recipient: str, custom_message: str):
        """
        Sends a custom message via SMS to the recipient.

        :param recipient: The recipient's phone number.
        :param custom_message: The message to send.
        :return: Success or failure status.
        """
        return await self.send_sms(recipient, custom_message)
