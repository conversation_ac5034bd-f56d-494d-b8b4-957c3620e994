from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import aifeedback

router = APIRouter(prefix="/aifeedback", tags=["aifeedback"])

@router.post("/aiform/create")
async def create_ai_form_route(
    data: schemas.AIFormCreate, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await aifeedback.create_ai_form(data, db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/aiform/{ai_form_id}/detail")
async def get_ai_form_detail_route(
    ai_form_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await aifeedback.get_ai_form_detail(ai_form_id, db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/aiform/{ai_form_id}/edit")
async def edit_ai_form_detail_route(
    ai_form_id: UUID, 
    data: schemas.EditAIFormRequest, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await aifeedback.edit_ai_form_detail(ai_form_id, data.form_title, db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/aiform/{ai_form_id}/feedback")
async def create_feedback_route(
    ai_form_id: UUID, 
    request: schemas.CreateFeedbackRequest, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await aifeedback.create_feedback(ai_form_id, [q.dict() for q in request.questions], db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/aiform/{ai_form_id}/customize-theme")
async def customize_ai_form_theme(
    ai_form_id: UUID,
    request: schemas.ThemeCustomizeRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    try:
        return await aifeedback.customize_theme(ai_form_id, request.dict(exclude_none=True), db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 


@router.post("/aiform/{ai_form_id}/setup-response-logic")
async def configure_response_logic(
    ai_form_id: UUID,
    request: schemas.AIFormResponseLogicRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    try:
        return await aifeedback.setup_ai_form_response_logic(ai_form_id, request.dict(exclude_none=True), db)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/aiform/{ai_form_id}/share-link")
async def get_ai_form_share_link(
    ai_form_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    try:
        return await aifeedback.get_share_ai_form(ai_form_id, db)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))