from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def get_user_profile(user_id: UUID, db: AsyncSession):
    query = await db.execute(
        select(
            models.User.user_image,
            (models.User.first_name + " " + models.User.last_name).label("full_name"),
            models.User.email,
            models.User.phone_number,
            models.User.job_title,
            func.count(models.Task.task_id).label("total_tasks"),
            func.count(models.Project.project_id).label("total_projects"),
            func.count(models.Event.event_id).label("total_events")
        )
        .join(models.Task, models.Task.user_id == models.User.user_id, isouter=True)
        .join(models.Project, models.Project.user_id == models.User.user_id, isouter=True)
        .join(models.Event, models.Event.user_id == models.User.user_id, isouter=True)
        .where(models.User.user_id == user_id)
        .group_by(models.User.user_id)
    )
    
    result = query.mappings().first()
    return result


pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def edit_user_profile(db: AsyncSession, user_id: UUID, payload: schemas.UserUpdate):
    result = await db.execute(select(models.User).filter(models.User.user_id == user_id))
    user = result.scalar_one_or_none()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if payload.old_password and payload.new_password:
        if not pwd_context.verify(payload.old_password, user.password):
            raise HTTPException(status_code=400, detail="Old password is incorrect")
        user.password = pwd_context.hash(payload.new_password)

    if payload.first_name:
        user.first_name = payload.first_name
    if payload.last_name:
        user.last_name = payload.last_name
    if payload.email:
        user.email = payload.email
    if payload.phone_number:
        user.phone_number = payload.phone_number
    if payload.job_title:
        user.job_title = payload.job_title

    await db.commit()
    await db.refresh(user)

    return {"message": "Profile updated successfully"}

async def integrate_crm(db: AsyncSession, payload: schemas.CRMIntegrationCreate):
    # Check if a similar integration already exists for the user
    result = await db.execute(
        select(models.CRMIntegration).filter(
            models.CRMIntegration.provider_name == payload.provider_name,
            models.CRMIntegration.user_id == payload.user_id
        )
    )
    existing = result.scalar_one_or_none()
    if existing:
        raise HTTPException(status_code=409, detail="CRM provider already integrated for this user.")

    crm = models.CRMIntegration(
        provider_name=payload.provider_name,
        url_link=str(payload.url_link),
        user_id=payload.user_id
    )
    db.add(crm)
    await db.commit()
    await db.refresh(crm)
    return {"message": "CRM integrated successfully", "crm_integration_id": crm.crm_integration_id}

async def get_user_billing_plan(db: AsyncSession, user_id: UUID) -> schemas.BillingPlanResponse:
    result = await db.execute(
        select(models.BillingPlan).filter(models.BillingPlan.user.has(user_id=user_id))
    )
    billing_plan = result.scalar_one_or_none()

    if not billing_plan:
        raise HTTPException(status_code=404, detail="Billing plan not found for user")

    return schemas.BillingPlanResponse.model_validate(billing_plan)

async def cancel_billing_plan(db: AsyncSession, user_email: str, plan_name: str) -> dict:
    result = await db.execute(
        select(models.BillingPlan).where(
            models.BillingPlan.user_email == user_email,
            models.BillingPlan.plan_name == plan_name
        )
    )
    billing_plan = result.scalar_one_or_none()

    if not billing_plan:
        raise HTTPException(status_code=404, detail="Billing plan not found")

    billing_plan.status = models.BillingStatus.CANCELED.value
    await db.commit()
    await db.refresh(billing_plan)

    return {"detail": "Billing plan canceled successfully"}

async def upgrade_plan(
    db: AsyncSession,
    user_email: str,
    current_plan: str,
    initial_plan: str,
    initial_amount: float,
    current_amount: float
) -> dict:
    # Cancel old plan (if it exists)
    existing_plan_query = await db.execute(
        select(models.BillingPlan).where(
            models.BillingPlan.user_email == user_email,
            models.BillingPlan.plan_name == initial_plan
        )
    )
    old_plan = existing_plan_query.scalar_one_or_none()

    if old_plan:
        old_plan.status = models.BillingStatus.CANCELED.value
        await db.commit()
        await db.refresh(old_plan)

    # Create new upgraded plan
    new_plan = models.BillingPlan(
        billing_plan_id=uuid4(),
        user_email=user_email,
        plan_name=current_plan,
        amount_paid=current_amount,
        initial_plan=initial_plan,
        status=BillingStatus.ACTIVE.value,
        billing_email=user_email,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )

    db.add(new_plan)
    await db.commit()
    await db.refresh(new_plan)

    return {"detail": "Plan upgraded successfully"}

async def add_payment_method(
    db: AsyncSession,
    user_email: str,
    payment_method: schemas.PaymentMethod
) -> dict:
    # Fetch the active billing plan for the user
    result = await db.execute(
        select(models.BillingPlan).where(
            models.BillingPlan.user_email == user_email,
            models.BillingPlan.status == "active"
        )
    )
    plan = result.scalar_one_or_none()

    if not plan:
        raise HTTPException(status_code=404, detail="Active billing plan not found for user")

    # Update payment method
    plan.payment_method = payment_method
    await db.commit()
    await db.refresh(plan)

    return {"detail": "Payment method added successfully"}