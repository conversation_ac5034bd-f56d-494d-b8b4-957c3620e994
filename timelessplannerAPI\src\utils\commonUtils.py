from uuid import uuid4,UUID
import re
from typing import Dict, Any

def generate_invite_link(team_id: UUID, base_url: str = "https://www.timelessplanner.com/invite") -> str:
    """
    Generate a unique invite link for the team based on team ID.
    """
    unique_token = uuid4()
    invite_link = f"{base_url}/{team_id}/{unique_token}"  # Construct the invite URL
    return invite_link

def is_email(contact: str) -> bool:
    """
    Validates if the given contact string is an email address.
    """
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, contact) is not None

def is_phone_number(contact: str) -> bool:
    """
    Validates if the given contact string is a phone number.
    Supports international formats, including optional '+' at the beginning.
    """
    phone_pattern = r'^\+?[\d\s()-]{7,15}$'  # Allows +, spaces, digits, hyphens, and parentheses
    return re.match(phone_pattern, contact) is not None


def validate_contact(contact: str) -> Dict[str, Any]:
    """
    Validates the contact string and determines its type.

    Returns:
        A dictionary with validation status and type (email or phone number).
    """
    if is_email(contact):
        return {
            "valid": True,
            "type": "email",
            "message": "Valid email address."
        }
    elif is_phone_number(contact):
        return {
            "valid": True,
            "type": "phone_number",
            "message": "Valid phone number."
        }
    else:
        return {
            "valid": False,
            "type": None,
            "message": "Invalid contact format. Please provide a valid email or phone number."
        }
