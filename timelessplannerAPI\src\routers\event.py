from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import event

router = APIRouter(prefix="/event", tags=["event"])

@router.post(
    "/add",
    response_model=schemas.EventRead,
    status_code=status.HTTP_201_CREATED,
    summary="Add a new event",
    description="Creates a new event linked to a project."
)
async def add_event_endpoint(
    event_in: schemas.EventCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.add_event(event_in, db, current_user.email)

@router.put(
    "/{event_id}/add_detail",
    response_model=schemas.EventRead,
    status_code=status.HTTP_200_OK,
    summary="Update an event with detailed information",
    description="Adds or updates event detail fields for an existing event using its event_id."
)
async def add_event_detail(
    event_id: UUID = Path(..., description="The UUID of the event to update."),
    event_detail: schemas.EventDetailUpdate = Depends(),
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.add_event_detail(event_id, event_detail, db)

@router.get(
    "/AllUser/event/overview",
    summary="Get all event overviews for a user",
    status_code=status.HTTP_200_OK
)
async def get_all_event_overview_route(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_all_event_overview(current_user.user_id, db)

@router.get(
    "/User/event/overview/{event_id}",
    summary="Get detailed overview of a single event",
    status_code=status.HTTP_200_OK
)
async def get_event_overview_route(
    event_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_overview_of_event(event_id, db)

@router.post(
    "/guest-tag/add",
    status_code=status.HTTP_201_CREATED,
    summary="Add a guest tag to an event",
    description="Adds a guest tag like VIP, Family, etc., with optional RSVP and name badge printing settings."
)
async def add_guest_tag_route(
    tag_data: schemas.GuestTagCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.add_guest_tag(tag_data, db)

@router.get(
    "/guest-tag/{guest_tag_id}/info",
    response_model=schemas.GuestTagInfoResponse,
    summary="Get information about a guest tag",
    description="Returns event details and guest stats for a specific guest tag."
)
async def get_guest_tag_info(
    guest_tag_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_guest_tag_info(guest_tag_id, db)

@router.get(
    "/guest-tag/{guest_tag_id}/overview",
    response_model= schemas.GuestTagOverviewResponse,
    summary="Get guest tag overview",
    description="Returns event name and list of guests with RSVP and check-in status"
)
async def get_guest_tag_overview_route(
    guest_tag_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_guest_tag_overview(guest_tag_id, db)

@router.post(
    "/guest/add",
    response_model=models.Guest,
    status_code=status.HTTP_201_CREATED,
    summary="Add guest to guest tag",
    description="Adds a guest with details to a specified guest tag"
)
async def add_guest(
    guest: schemas.GuestCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.add_guest_to_guest_tag(guest, db)


@router.post(
    "/notification-setting/add",
    response_model=schemas.NotificationSettingRead,
    status_code=status.HTTP_201_CREATED,
    summary="Add notification setting",
    description="Adds notification preferences (email and text) for a guest"
)
async def add_notification_setting_endpoint(
    setting: schemas.NotificationSettingCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await add_notification_setting(setting, db)

@router.get(
    "/guest-tag/overview/{guest_id}",
    response_model=schemas.GuestTagOverview,
    status_code=status.HTTP_200_OK,
    summary="Get a single guest tag overview",
    description="Returns overview of a specific guest with tag info, external ID, RSVP and check-in status"
)
async def get_guest_tag_overview_route(
    guest_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_a_guest_tag_overview(guest_id, db)

@router.get(
    "/guest/overview/{guest_id}",
    response_model=schemas.AGuestOverview,
    status_code=status.HTTP_200_OK,
    summary="Get guest detailed overview",
    description="Returns full details about a guest including tag, seating, and affiliations"
)
async def get_guest_overview_route(
    guest_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_a_guest_overview(guest_id, db)

@router.post(
    "/guest/upload",
    status_code=status.HTTP_200_OK,
    summary="Upload guests via CSV or Excel",
    description="Accepts CSV or Excel file with guest data and stores it in the database."
)
async def upload_guests_route(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.upload_guests(file, db)

@router.get(
    "/guest/template/csv",
    summary="Download Guest CSV Template",
    description="Download a blank CSV template for uploading guest data."
)
async def download_guest_csv_template(
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return event.download_guest_csv()


@router.get(
    "/guest/template/excel",
    summary="Download Guest Excel Template",
    description="Download a blank Excel template for uploading guest data."
)
async def download_guest_excel_template(
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return event.download_guest_excel()

@router.post(
    "/guest-tag/add-guest-form",
    status_code=status.HTTP_201_CREATED,
    summary="Add Guest to Guest Tag (Simplified Form)",
    description="Add a guest to a guest tag using salutation, first name, email, and phone number."
)
async def create_guest_form(
    guest_data: schemas.GuestSimpleCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.create_guest_form_to_add_to_tag(guest_data, db)

@router.get(
    "/guest/{guest_id}/form-detail",
    response_model=schemas.GuestFormDetailOut,
    status_code=status.HTTP_200_OK,
    summary="Get detail for guest form",
    description="Returns event name, event description, and email for a given guest form"
)
async def get_guest_form_detail(
    guest_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_detail_for_guest_form(guest_id, db)

@router.get(
    "/print/event/{event_id}/guests",
    response_model=List[schemas.GuestOut],
    status_code=status.HTTP_200_OK,
    summary="List all guests added to an event",
    description="Returns full guest details for all guests associated with a given event to enable printing"
)
async def list_guests_on_event(
    event_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.print_added_guest_on_event(event_id, db)

@router.get(
    "/upcoming",
    response_model=List[schemas.EventOut],
    status_code=status.HTTP_200_OK,
    summary="Get upcoming events",
    description="Returns a list of events that have not yet started"
)
async def list_upcoming_events(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_upcoming_events(db)


@router.get(
    "/past",
    response_model=List[schemas.EventOut],
    status_code=status.HTTP_200_OK,
    summary="Get past events",
    description="Returns a list of events that have already taken place."
)
async def list_past_events(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await event.get_past_events(db)


