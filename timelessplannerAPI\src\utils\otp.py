# utils/otp.py

import random
from datetime import datetime, timedelta, timezone
from uuid import UUID
from fastapi import HTT<PERSON>Exception
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from src.services import sms_service,whatsapp_service
from src.services.email_service_smtp import EmailService
from src.models import models
from src.schemas import schemas

async def generate_otp(length: int = 6) -> str:
    """
    Generate a random numeric OTP of a given length.
    """
    return ''.join(random.choices('0123456789', k=length))

async def create_user_otp(db: AsyncSession, user_id: UUID, otp_code: str) -> schemas.UserOTPOut:
    """
    Creates a UserOTP record in the database.
    """
    otp_record = models.UserOTP(
        user_id=user_id,
        otp_code=otp_code,
        otp_type='EMAIL',  # Since this function is specific to email
        created_at= models.get_current_utc_time,
        expires_at=models.get_current_utc_time + timedelta(minutes=10),
        is_used=False
    )

    db.add(otp_record)
    await db.commit()
    await db.refresh(otp_record)

    return otp_record


async def send_otp(user_contact: str, method: str, otp_code: str) -> None:
    """
    Send the OTP to the user via the specified method.
    
    Parameters:
    - user_contact: The user's contact information (email, phone number).
    - method: The method to use for sending the OTP ('email', 'sms', 'whatsapp')
    - otp_code: The OTP code to be sent.
    """
    if method == "email":
        await EmailService.send_otp_email(user_contact, otp_code)
    elif method == "sms":
        await sms_service.send_otp_sms(user_contact, otp_code)
    elif method == "whatsapp":
        await whatsapp_service.send_otp_whatsapp(user_contact, otp_code)
    else:
        raise HTTPException(status_code=400, detail="Invalid OTP sending method")


async def save_otp(db: AsyncSession, user_id: int, otp_code: str, expires_in_minutes: int = 10) -> models.UserOTP:
    """
    Save the OTP to the database with an expiration time.
    """
    otp_entry = models.UserOTP(
        user_id=user_id,
        otp_code=otp_code,
        expires_at=datetime.now(timezone.utc) + timedelta(minutes=expires_in_minutes)
    )
    db.add(otp_entry)
    await db.commit()
    await db.refresh(otp_entry)
    return otp_entry


async def validate_otp(db: AsyncSession, user_id: int, otp_code: str) -> bool:
    """
    Validate the OTP code by checking against the stored OTP in the database.
    """
    otp_record = await db.execute(
        select(models.UserOTP).where(models.UserOTP.user_id == user_id).where(models.UserOTP.otp_code == otp_code)
    )
    otp_entry = otp_record.scalar_one_or_none()

    if otp_entry and otp_entry.expires_at > datetime.now(timezone.utc):
        return True
    
    return False
