
@router.get("/users", response_model=List[schemas.UserOut], status_code=status.HTTP_200_OK)
async def read_users(
    skip: int = 1, 
    limit: int = 100, 
    db: AsyncSession = Depends(get_session),
    current_user:dict = Depends(get_current_active_user)
    ) -> List[schemas.UserOut]:
    """
    Get a list of users with pagination.
    """
    return await crud.users.get_users(db, skip=skip, limit=limit)

@router.get("/{user_id}", response_model=schemas.UserOut, status_code=status.HTTP_200_OK)
async def read_user(user_id: UUID, db: AsyncSession = Depends(get_session)) -> schemas.UserOut:
    """
    Get a single user by their ID.
    """
    user = await crud.users.get_user(db, user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return user


@router.put("/{user_id}", response_model=schemas.UserOut, status_code=status.HTTP_200_OK)
async def update_user_endpoint(
    user_id: UUID, 
    user_update: schemas.UserUpdate, 
    db: AsyncSession = Depends(get_session)                         
    ) -> schemas.UserOut:
    """
    Update a user's information.
    """
    updated_user = await crud.users.update_user(db, user_id, user_update)
    if not updated_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")
    return updated_user

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: dict = Depends(get_current_active_user)
):
    """
    Delete a user by their ID.
    """
    # Retrieve the user from the database (this is a model instance, not a Pydantic schema)
    user = await crud.users.get_user(db, user_id)
    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    try:
        # Manually delete associated tokens before deleting the user
        await crud.token.delete_tokens_by_user_id(db, user_id)
        
        # Delete the user using the model instance
        await db.delete(user)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"An error occurred: {str(e)}")
