from src.utils.commonImports import *
from src.models import models
from src.schemas import  schemas

async def get_total_projects(db: AsyncSession, user_email: str) -> int:
    result = await db.execute(
        select(func.count(models.Project.project_id)).where(models.Project.user_email == user_email)
    )
    total_projects = result.scalar() or 0
    return total_projects

async def get_total_events(db: AsyncSession, user_email: str) -> int:
    result = await db.execute(
        select(func.count(models.Event.event_id))
        .join(models.Project)
        .where(models.Project.user_email == user_email)
    )
    total_events = result.scalar() or 0
    return total_events

async def get_total_tasks(db: AsyncSession, user_id: UUID) -> int:
    result = await db.execute(
        select(func.count(models.Task.task_id)).where(models.Task.user_id == user_id)
    )
    total_tasks = result.scalar() or 0
    return total_tasks

# ==============================
# getRecentEvent By user Function
# ==============================
async def get_recent_events_by_user(
    user_id: UUID,
    db: AsyncSession
) -> List[schemas.RecentEvent]:
    # Join Event -> Project, filter by user_id, order by creation, limit 6
    stmt = (
        select(models.Event.event_name, models.Project.project_name)
        .join(models.Project, models.Event.project_id == models.Project.project_id)
        .where(models.Event.user_id == user_id)
        .order_by(models.Event.created_at.desc())
        .limit(6)
    )
    result = await db.execute(stmt)
    rows = result.all()
    # Map to Pydantic schema
    return [schemas.RecentEvent(event_name=row.event_name, project_name=row.project_name) for row in rows]

# ==============================
# CRUD Function
# ==============================
async def get_recent_tasks_by_user(
    user_id: UUID,
    db: AsyncSession
) -> List[schemas.RecentTask]:
    # Select required fields and join Task -> Event -> Project
    stmt = (
        select(
            models.Project.project_name,
            models.Event.event_name,
            models.Task.task_name,
            models.Task.task_status,
            models.Task.days_left,
            models.Task.team_member_images,
            models.Task.total_chats
        )
        .join(models.Event, models.Task.event_id == models.Event.event_id)
        .join(models.Project, models.Event.project_id == models.Project.project_id)
        .where(models.Task.user_id == user_id)
        .order_by(models.Task.created_at.desc())
        .limit(2)
    )
    result = await db.execute(stmt)
    rows = result.all()

    recent_tasks: List[schemas.RecentTask] = []
    for project_name, event_name, task_name, status, days_left, images_csv, chats in rows:
        # Split CSV of images and take first two
        images = []
        if images_csv:
            images = [img for img in images_csv.split(',') if img][:2]

        recent_tasks.append(
            schemas.RecentTask(
                project_name=project_name,
                event_name=event_name,
                task_name=task_name,
                task_status=status,
                days_left=days_left,
                member_images=images,
                total_chats=chats,
            )
        )
    return recent_tasks

async def get_team_chats_by_user(
    user_id: UUID,
    db: AsyncSession
) -> List[schemas.TeamChatSummary]:
    # 1. find team_ids where user is a member
    team_ids_stmt = select(models.TeamMember.team_id).where(models.TeamMember.user_id == user_id)
    team_ids_result = await db.execute(team_ids_stmt)
    team_ids = [row.team_id for row in team_ids_result]

    if not team_ids:
        return []

    # 2. fetch first 2 chat sessions for those teams
    chats_stmt = (
        select(models.ChatSession)
        .where(models.ChatSession.team_id.in_(team_ids))
        .order_by(models.ChatSession.created_at.desc())
        .limit(2)
    )
    chats_result = await db.execute(chats_stmt)
    chat_sessions = chats_result.scalars().all()

    summaries: List[schemas.TeamChatSummary] = []
    for chat in chat_sessions:
        # fetch member images (first 5)
        mem_stmt = (
            select(models.TeamMember.image)
            .where(models.TeamMember.team_id == chat.team_id, models.TeamMember.image != None)
            .limit(5)
        )
        mem_res = await db.execute(mem_stmt)
        images = [row.image for row in mem_res if row.image]

        # count leads (role contains 'lead', case-insensitive)
        lead_stmt = (
            select(func.count(models.TeamMember.team_member_id))
            .where(models.TeamMember.team_id == chat.team_id,
                   func.lower(models.TeamMember.role).like('%lead%'))
        )
        lead_res = await db.execute(lead_stmt)
        total_leads = lead_res.scalar_one()

        # count total members
        total_stmt = (
            select(func.count(models.TeamMember.team_member_id))
            .where(models.TeamMember.team_id == chat.team_id)
        )
        total_res = await db.execute(total_stmt)
        total_members = total_res.scalar_one()

        summaries.append(
            schemas.TeamChatSummary(
                chat_session_id=chat.chat_session_id,
                session_name=chat.session_name,
                member_images=images,
                total_leads=total_leads,
                total_members=total_members,
            )
        )
    return summaries


async def get_team_leads(
    user_id: UUID,
    db: AsyncSession
) -> List[schemas.TeamLeadEvent]:
    # 1. Find teams where this user is a lead
    lead_teams_stmt = (
        select(models.TeamMember.team_id, models.TeamMember.image, models.TeamMember.email)
        .where(
            models.TeamMember.user_id == user_id,
            func.lower(models.TeamMember.role).like('%lead%')
        )
    )
    lead_teams_res = await db.execute(lead_teams_stmt)
    lead_rows = lead_teams_res.all()
    if not lead_rows:
        return []

    summaries: List[schemas.TeamLeadEvent] = []
    # 2. For each team, fetch its event and project, limit to first 3 events
    count = 0
    for team_id, image, email in lead_rows:
        if count >= 3:
            break
        # Fetch associated event
        team_stmt = select(models.Team).where(models.Team.team_id == team_id)
        team_obj = (await db.execute(team_stmt)).scalar_one_or_none()
        if not team_obj or not team_obj.event_id:
            continue

        # Fetch event and project
        event_stmt = (
            select(models.Event.event_name, models.Project.project_name)
            .join(models.Project, models.Event.project_id == models.Project.project_id)
            .where(models.Event.event_id == team_obj.event_id)
        )
        ev_res = await db.execute(event_stmt)
        ev_row = ev_res.first()
        if not ev_row:
            continue

        event_name, project_name = ev_row
        summaries.append(
            schemas.TeamLeadEvent(
                image=image,
                email=email,
                team_name=team_obj.team_name,
                project_name=project_name,
                event_name=event_name,
            )
        )
        count += 1

    return summaries