# app/services/database.py
from src.utils.commonImports import *
from src.utils.config import settings
import logging
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker, AsyncConnection
from contextlib import asynccontextmanager
from typing import Optional, AsyncIterator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseSessionManager:
    """
    Manages database connections and sessions asynchronously.
    Implements singleton pattern to ensure only one instance exists.
    """
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseSessionManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._engine: Optional[AsyncEngine] = None
            self._sessionmaker: Optional[async_sessionmaker] = None
            self._initialized = True

    def init(self, host: str, echo: bool = False):
        """
        Initialize the database engine and session maker.
        
        Args:
            host (str): Database connection string
            echo (bool): Whether to echo SQL statements (default: False)
        """
        if self._engine is not None:
            logger.warning("DatabaseSessionManager is already initialized")
            return

        try:
            self._engine = create_async_engine(
                host,
                pool_pre_ping=True,
                pool_size=10,
                max_overflow=20,
                pool_timeout=60,
                echo=echo 
            )
            
            self._sessionmaker = async_sessionmaker(
                bind=self._engine,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
            )
        except Exception as e:
            logger.error(f"Failed to initialize database engine: {e}")
            raise

    async def close(self):
        """Close the database engine and clean up resources."""
        if self._engine is None:
            raise Exception("DatabaseSessionManager is not initialized")
        
        await self._engine.dispose()
        self._engine = None
        self._sessionmaker = None
        self._initialized = False

    @asynccontextmanager
    async def connect(self) -> AsyncIterator[AsyncConnection]:
        """Get a database connection."""
        if self._engine is None:
            raise Exception("DatabaseSessionManager is not initialized")
        
        async with self._engine.begin() as connection:
            try:
                yield connection
            except Exception as ex:
                await connection.rollback()
                logger.error(f"Error during connection: {ex}")
                raise

    @asynccontextmanager
    async def session(self) -> AsyncIterator[AsyncSession]:
        """Get a database session."""
        if self._sessionmaker is None:
            raise Exception("DatabaseSessionManager is not initialized")
        
        session = self._sessionmaker()
        try:
            yield session
        except Exception as ex:
            await session.rollback()
            logger.error(f"Error during session: {ex}")
            raise
        finally:
            await session.close()

    async def create_all(self, connection: AsyncConnection):
        """Create all database tables."""
        await connection.run_sync(Base.metadata.create_all)

    async def drop_all(self, connection: AsyncConnection):
        """Drop all database tables."""
        await connection.run_sync(Base.metadata.drop_all)


# Initialize the database URL
DATABASE_URL = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")

# Create a single instance of DatabaseSessionManager with echo option
try:
    # Try to get debug mode from settings, fallback to False if not found
    debug_mode = getattr(settings, 'debug_mode', False)
except AttributeError:
    debug_mode = False

# Create a single instance of DatabaseSessionManager
sessionmanager = DatabaseSessionManager()
sessionmanager.init(DATABASE_URL, echo=debug_mode)

# Dependency for FastAPI
async def get_session() -> AsyncIterator[AsyncSession]:
    """Dependency that provides a database session."""
    async with sessionmanager.session() as session:
        yield session