# src/utils/config.py

import os
from dotenv import load_dotenv
# Pydantic v2
from pydantic import ConfigDict, Field, EmailStr, SecretStr, BeforeValidator, HttpUrl, TypeAdapter
# Pydantic-Settings
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Annotated

# Load environment variables from .env file
load_dotenv()

# Adapter to coerce strings into valid HttpUrl values
http_url_adapter = TypeAdapter(HttpUrl)
Url = Annotated[str, BeforeValidator(lambda value: str(http_url_adapter.validate_python(value)))]

class Settings(BaseSettings):
    # ── Tell Pydantic to load .env and ignore any extra keys ────────────────
    model_config = ConfigDict(
        env_file = ".env",
        env_file_encoding = "utf-8",
        extra    = "ignore"
    )

    # Database settings
    DATABASE_URL: str = Field(default=os.getenv("DATABASE_URL"))
    DEV_DATABASE_URL: str = Field(default=os.getenv("DEV_DATABASE_URL"))

    # API settings
    API_KEY: SecretStr = Field(default=os.getenv("API_KEY"))
    APP_NAME: str = Field(default=os.getenv("APP_NAME"))
    ADMIN_EMAIL: EmailStr = Field(default=os.getenv("ADMIN_EMAIL"))

    # JWT settings
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 1440)))
    REFRESH_TOKEN_EXPIRE_MINUTES: int = Field(default=int(os.getenv("REFRESH_TOKEN_EXPIRE_MINUTES", 10080)))
    JWT_ALGORITHM: str = Field(default=os.getenv("JWT_ALGORITHM", "HS256"))
    JWT_SECRET_KEY: SecretStr = Field(default=os.getenv("JWT_SECRET_KEY"))
    JWT_REFRESH_SECRET_KEY: SecretStr = Field(default=os.getenv("JWT_REFRESH_SECRET_KEY"))
    JWT_ISSUER: str = Field(default=os.getenv("JWT_ISSUER"))
    JWT_AUDIENCE: str = Field(default=os.getenv("JWT_AUDIENCE"))
    JWT_EXPIRATION: int = Field(default=int(os.getenv("JWT_EXPIRATION", 3600)))
    JWT_REFRESH_EXPIRATION: int = Field(default=int(os.getenv("JWT_REFRESH_EXPIRATION", 604800)))

    # Optional settings
    DEBUG: bool = Field(default=os.getenv("DEBUG", "False").lower() == "true")

    # Google API Settings
    GOOGLE_CLIENT_ID: str = Field(default=os.getenv("GOOGLE_CLIENT_ID"))
    GOOGLE_CLIENT_SECRET: SecretStr = Field(default=os.getenv("GOOGLE_CLIENT_SECRET"))
    GOOGLE_REDIRECT_URI: Url = Field(default=os.getenv("GOOGLE_REDIRECT_URI"))
    GOOGLE_AUTH_URL: Url = Field(default=os.getenv("GOOGLE_AUTH_URL"))
    GOOGLE_TOKEN_URL: Url = Field(default=os.getenv("GOOGLE_TOKEN_URL"))
    GOOGLE_USER_INFO_URL: Url = Field(default=os.getenv("GOOGLE_USER_INFO_URL"))

    #Google Places Setting
    GOOGLE_API_KEY : SecretStr = Field(default=os.getenv("GOOGLE_API_KEY"))
    PLACES_SEARCH_URL: Url = Field(default=os.getenv("PLACES_SEARCH_URL"))
    PLACE_DETAILS_URL: Url = Field(default=os.getenv("PLACE_DETAILS_URL"))
    
    # Facebook API Setting
    FACEBOOK_CLIENT_ID: str = Field(default=os.getenv("FACEBOOK_CLIENT_ID"))
    FACEBOOK_CLIENT_SECRET: SecretStr = Field(default=os.getenv("FACEBOOK_CLIENT_SECRET"))
    FACEBOOK_AUTHORIZE_URL: Url = Field(default=os.getenv("FACEBOOK_AUTHORIZE_URL"))
    FACEBOOK_TOKEN_URL: Url = Field(default=os.getenv("FACEBOOK_TOKEN_URL"))
    FACEBOOK_USERINFO_URL: Url = Field(default=os.getenv("FACEBOOK_USERINFO_URL"))
    FACEBOOK_REDIRECT_URL: Url = Field(default=os.getenv("FACEBOOK_REDIRECT_URL"))
    
    # Frontend and invite URLs
    FRONTEND_URL: Url = Field(default=os.getenv("FRONTEND_URL"))
    INVITE_URL: Url = Field(default=os.getenv("INVITE_URL"))
    SUB_TASK_URL: Url = Field(default=os.getenv("SUB_TASK_URL"))

    # Environment setting
    ENVIRONMENT: str = Field(default=os.getenv("ENVIRONMENT"))

    # Email campaign
    MAIL_SERVER: str = Field(default=os.getenv("MAIL_SERVER"))
    MAIL_PORT: int = Field(default=int(os.getenv("MAIL_PORT", 0)))
    MAIL_USERNAME: EmailStr = Field(default=os.getenv("MAIL_USERNAME"))
    MAIL_PASSWORD: str = Field(default=os.getenv("MAIL_PASSWORD"))
    MAIL_FROM: EmailStr = Field(default=os.getenv("MAIL_FROM"))
    MAIL_FROM_NAME: str = Field(default=os.getenv("MAIL_FROM_NAME"))
    MAIL_USE_TLS: bool = Field(default=os.getenv("MAIL_USE_TLS", "False").lower() == "true")
    MAIL_USE_SSL: bool = Field(default=os.getenv("MAIL_USE_SSL", "False").lower() == "true")

    # BulkSMS Nigeria
    BULKSMSNIGERIA_API_KEY: str = Field(default=os.getenv("BULKSMSNIGERIA_API_KEY"))
    BULKSMSNIGERIA_API_URL: Url = Field(default=os.getenv("BULKSMSNIGERIA_API_URL"))
    SMS_BALANCE_URL: Url = Field(default=os.getenv("SMS_BALANCE_URL"))
    SMS_DELIVERY_REPORT_URL: Url = Field(default=os.getenv("SMS_DELIVERY_REPORT_URL"))

    # eBulkSMS
    EBULKSMS_USERNAME: EmailStr = Field(default=os.getenv("EBULKSMS_USERNAME"))
    EBULKSMS_API_KEY: str = Field(default=os.getenv("EBULKSMS_API_KEY"))

    # WhatsApp settings
    WHATSAPP_ACCESS_TOKEN: str = Field(default=os.getenv("WHATSAPP_ACCESS_TOKEN"))
    WHATSAPP_PHONE_NUMBER_ID: str = Field(default=os.getenv("WHATSAPP_PHONE_NUMBER_ID"))

    # VAPID keys for push notifications
    VAPID_PRIVATE_KEY: str = Field(default=os.getenv("VAPID_PRIVATE_KEY"))
    VAPID_PUBLIC_KEY: str = Field(default=os.getenv("VAPID_PUBLIC_KEY"))

    # Amazon S3
    AMAZON_ACCESS_KEY: str = Field(default=os.getenv("AMAZON_ACCESS_KEY"))
    AMAZON_SECRET_ACCESS_KEY: str = Field(default=os.getenv("AMAZON_SECRET_ACCESS_KEY"))
    AMAZON_REGION: str = Field(default=os.getenv("AMAZON_REGION"))

    #Mailtrap Setting
    MAILTRAP_ACCESS: str = Field(default=os.getenv("MAILTRAP_ACCESS"))

    @property
    def database_url(self) -> str:
        if self.ENVIRONMENT == "dev":
            if not self.DEV_DATABASE_URL:
                raise ValueError("DEV_DATABASE_URL is missing or empty.")
            return self.DEV_DATABASE_URL
        else:
            if not self.DATABASE_URL:
                raise ValueError("DATABASE_URL is missing or empty.")
            return self.DATABASE_URL

# Create a global instance
settings = Settings()

# Dependency for FastAPI
def get_settings() -> Settings:
    return settings
