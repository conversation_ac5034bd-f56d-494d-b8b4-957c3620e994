from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def get_list_of_guest_tags(db: AsyncSession):
    result = await db.execute(
        select(models.GuestTag)
        .options(
            joinedload(models,GuestTag.guests),
            joinedload(models.GuestTag.event),
            joinedload(models.GuestTag.broadcast)
        )
    )
    guest_tags = result.scalars().all()

    data = []
    for tag in guest_tags:
        if not tag.broadcast:
            continue  # Only include if associated with a broadcast

        for bcast in tag.broadcast:
            data.append({
                "tag_name": tag.tag_name,
                "event_name": tag.event.event_name if tag.event else None,
                "total_guests": len(tag.guests),
                "message": bcast.message,
                "status": bcast.status
            })

    return data

async def setup_broadcast(data: schemas.BroadcastCreate, db: AsyncSession):
    # Create the broadcast
    broadcast = models.Broadcast(
        broadcast_id=uuid4(),
        event_id=data.event_id,
        message_subject=data.message_subject,
        message=data.message,
        medium=data.medium,
        status="not-sent",
        created_at=datetime.utcnow()
    )
    db.add(broadcast)
    await db.flush()  # Ensure the broadcast_id is available

    # Link the broadcast to guest tags
    guest_tags = await db.execute(
        select(models.GuestTag).where(models.GuestTag.guest_tag_id.in_(data.guest_tag_ids))
    )
    for tag in guest_tags.scalars().all():
        tag.broadcast.append(broadcast)  # assuming many-to-many or similar logic

    await db.commit()
    await db.refresh(broadcast)
    return {"message": "Broadcast created successfully", "broadcast_id": broadcast.broadcast_id}