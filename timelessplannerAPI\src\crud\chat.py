from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas
from datetime import datetime

import calendar

async def get_user_profile(user_id: UUID, db: AsyncSession) -> schemas.UserProfileOut:
    user = await db.get(models.User, user_id)
    return schemas.UserProfileOut(
        full_name=user.full_name,
        email=user.email,
        image=user.image
    )

async def search_chat_by_task_name(task_name: str, db: AsyncSession) -> schemas.ChatSessionWithMessages:
    result = await db.execute(
        select(models.ChatSession)
        .join(models.ChatSession.task)
        .options(
            joinedload(models.ChatSession.chat_messages),
            joinedload(models.ChatSession.task)
        )
        .where(models.Task.task_name.ilike(f"%{task_name}%"))
    )
    
    chat_session = result.scalars().first()
    
    if not chat_session:
        raise HTTPException(status_code=404, detail="Chat session not found for the task name")
    
    return schemas.ChatSessionWithMessages(
        chat_session_id=chat_session.chat_session_id,
        session_name=chat_session.session_name,
        created_at=chat_session.created_at,
        task_name=chat_session.task.task_name,
        messages=chat_session.chat_messages
    )

async def get_all_task_chat(db: AsyncSession) -> List[schemas.TaskChatSummary]:
    result = await db.execute(
        select(models.ChatSession)
        .options(
            joinedload(models.ChatSession.task),
            joinedload(models.ChatSession.team).joinedload(models.Team.members)
        )
    )

    chat_sessions = result.scalars().all()
    summaries = []

    for session in chat_sessions:
        if session.task and session.team:
            images = [
                schemas.TaskChatMemberImage(image=member.image)
                for member in session.team.members[:5]
            ]

            summaries.append(schemas.TaskChatSummary(
                task_name=session.task.task_name,
                team_name=session.team.team_name,
                member_images=images
            ))

    return summaries

async def get_team_message_detail(team_id: UUID, db: AsyncSession) -> schemas.TeamMessageDetail:
    # Get the team
    result = await db.execute(
        select(models.Team)
        .options(joinedload(models.Team.members))
        .where(models.Team.team_id == team_id)
    )
    team = result.scalars().first()
    if not team:
        raise ValueError("Team not found")

    # Fetch last 5 messages from team members
    msg_result = await db.execute(
        select(models.ChatMessage)
        .join(models.TeamMember, models.ChatMessage.sender_id == models.TeamMember.team_member_id)
        .where(models.TeamMember.team_id == team_id)
        .order_by(models.ChatMessage.created_at.desc())
        .limit(5)
        .options(joinedload(models.ChatMessage.sender))
    )

    messages = msg_result.scalars().all()
    images = []
    seen_member_ids = set()

    for msg in messages:
        sender = msg.sender
        if sender and sender.team_member_id not in seen_member_ids:
            seen_member_ids.add(sender.team_member_id)
            images.append(schemas.MemberImage(image=sender.image))

    return schemas.TeamMessageDetail(
        team_name=team.team_name,
        member_images=images
    )

def format_day_time(dt: datetime) -> str:
    return f"{calendar.day_name[dt.weekday()]}, {dt.strftime('%I:%M %p')}"

async def get_chat_session_messages(chat_session_id: UUID, db: AsyncSession) -> schemas.ChatSessionMessages:
    # Get chat session
    result = await db.execute(
        select(models.ChatSession)
        .options(joinedload(models.ChatSession.team))
        .where(models.ChatSession.chat_session_id == chat_session_id)
    )
    chat_session = result.scalars().first()
    if not chat_session:
        raise ValueError("Chat session not found")

    team = chat_session.team
    team_name = team.team_name if team else "Unknown Team"

    # Get all messages in chat session
    result = await db.execute(
        select(models.ChatMessage)
        .where(models.ChatMessage.chat_session_id == chat_session_id)
        .options(joinedload(models.ChatMessage.sender))
        .order_by(models.ChatMessage.created_at.asc())
    )
    messages = result.scalars().all()

    # Unique list of last 5 senders for team member images
    seen = {}
    for msg in reversed(messages):
        sender = msg.sender
        if sender and sender.team_member_id not in seen:
            seen[sender.team_member_id] = sender.image
        if len(seen) == 5:
            break

    member_images = list(seen.values())

    message_list = [
        schemas.MessageDetail(
            sender_name=msg.sender.member_name if msg.sender else "Unknown",
            sender_image=msg.sender.image if msg.sender else None,
            day_time=format_day_time(msg.created_at),
            message=msg.message,
            attachment=msg.attachment,
            media=msg.media
        )
        for msg in messages
    ]

    return schemas.ChatSessionMessages(
        team_name=team_name,
        member_images=member_images,
        messages=message_list
    )

async def send_message(data: schemas.SendMessageCreate, db: AsyncSession):
    # Verify chat session exists and matches team and task
    chat_session = await db.get(models.ChatSession, data.chat_session_id)
    if not chat_session or chat_session.team_id != data.team_id or chat_session.task_id != data.task_id:
        raise ValueError("Invalid chat session or mismatch with team/task.")

    message = models.ChatMessage(
        message_id=uuid4(),
        chat_session_id=data.chat_session_id,
        sender_id=data.sender_id,
        message=data.message,
        attachment=data.attachment,
        media=data.media,
        created_at=datetime.utcnow()
    )
    db.add(message)
    await db.commit()
    await db.refresh(message)
    return message