import asyncio
from src.services.email_service_smtp import EmailService,SMTPMailer

async def test_send_live_email():
    recipient = "<EMAIL>" 
    subject = "✅ Live Test: TimelessPlanner SMTP Email"
    body = (
        "This is a live test email sent from the EmailService.send_email function.\n"
        "If you're seeing this, it worked successfully!"
    )

    try:
        result = await EmailService.send_email(subject=subject, recipients=recipient, body=body)
        print("✅ Email sent successfully:", result)
    except Exception as e:
        print("❌ Failed to send email:", str(e))


if __name__ == "__main__":
    asyncio.run(test_send_live_email())
