from pydantic import BaseModel,ConfigDict, EmailStr, HttpUrl, Field,field_validator
from uuid import UUID
from datetime import datetime, date, time
from typing import Union,Literal, Optional, List
from enum import Enum


# ===============================
# User Schemas
# ===============================

class UserBase(BaseModel):
    first_name: str | None = None
    last_name: str | None = None
    job_title: str | None = None
    email: EmailStr
    phone_number: str | None = None
    is_active: bool = False
    provider: str | None = None 
    provider_id: str | None = None
    user_image: str | None = None

class UserCreate(UserBase):
    password: str
class UserUpdate(BaseModel):
    first_name: str | None = None
    last_name: str | None = None
    job_title: str | None = None
    phone_number: str | None = None
    password: str | None = None
    is_active: bool | None = Field(default=None, description="Set to true to activate the user")
    provider: str | None = None
    provider_id: str | None = None
    user_image: str | None = None

    class Config:
        from_attributes = True

class UserInDB(UserBase):
    user_id: UUID
    password: str
    created_at: datetime
    updated_at: datetime

class UserOut(UserBase):
    user_id: UUID
    created_at: datetime | None = None
    updated_at: datetime | None = None

    class Config:
        from_attributes = True

class PasswordResetRequest(BaseModel):
    email: EmailStr

class ChangePassword(BaseModel):
    email: EmailStr
    new_password:str
   
# ===============================
# PasswordResetToken Schemas
# ===============================
class PasswordResetTokenCreate(BaseModel):

    user_id: UUID
    token:  str      
    expires_at:  datetime

    model_config = ConfigDict(
        extra="ignore"
    )


class PasswordResetTokenRead(BaseModel):
    password_reset_id: UUID
    user_id:     UUID
    token:       str
    created_at:  datetime
    expires_at:  datetime
    used:        bool

    model_config = ConfigDict(
        from_attributes=True  # read values from ORM model attributes
    )

class VerifyResetLinkRequest(BaseModel):
    token: str


# ===============================
# Token Schemas
# ===============================

class TokenBase(BaseModel):
    user_id: UUID
    access_token: str
    refresh_token: str
    status: bool

class TokenCreate(TokenBase):
    pass

class TokenOut(TokenBase):
    token_id: UUID
    created_at: datetime
    revoked_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class TokenRefreshRequest(BaseModel):
    refresh_token: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str

# ===============================
# OTP Schemas
# ===============================

class UserOTPBase(BaseModel):
    user_id: UUID
    email: EmailStr
    otp_code: str

class UserOTPCreate(UserOTPBase):
    is_used: bool | None = None

class UserOTPOut(UserOTPBase):
    otp_id: UUID
    created_at: datetime
    expires_at: datetime
    is_used: bool

    class Config:
        from_attributes = True


class UserOTPUpdate(BaseModel):
    is_used: bool | None = None

class OTPVerificationRequest(BaseModel):
    email: EmailStr
    otp_code: str

class SendOTPRequest(BaseModel):
    email: EmailStr

# ===============================
# PROJECT Schemas
# ===============================

class ProjectBase(BaseModel):
    project_name: str = Field(..., max_length=200, description="Name of the project")
    project_description: Optional[str] = Field(None, description="Detailed description of the project")
    user_email: str = Field(..., description="Email of the user who created the project")

    model_config = ConfigDict(from_attributes=True)

class ProjectCreate(ProjectBase):
    pass

class ProjectRead(BaseModel):
    project_id: UUID
    project_name: str
    project_description: Optional[str]
    user_email: str
    created_at: datetime
    event_count: int

    model_config = ConfigDict(from_attributes=True)

class ProjectOverviewRead(BaseModel):
    project_id: UUID
    user_email: str
    project_name: str
    project_description: Optional[str]
    created_day: int
    created_month: int
    created_year: int
    created_time: time
    created_at: datetime
    event_count: int

    model_config = ConfigDict(from_attributes=True)


class ProjectOverview(BaseModel):
    project_name: str
    project_description: Optional[str]
    created_day: int = Field(..., description="Day of month project was created")
    created_month: int = Field(..., description="Month number project was created")
    created_year: int = Field(..., description="Year project was created")
    created_time: time = Field(..., description="Time of day project was created")
    event_count: int = Field(..., description="Total number of events in the project")
    created_at:datetime

    model_config = ConfigDict(from_attributes=True)

class ProjectEventCreate(BaseModel):
    event_name: str = Field(..., max_length=200)
    team: bool = Field(default=False)
  
# ===============================
# EVENT Schemas
# ===============================

class EventBase(BaseModel):
    event_name: str = Field(..., max_length=200, description="Name of the event")
    project_id: UUID = Field(..., description="ID of the associated project")
    team: bool = Field(False, description="Whether this is a team event")
    event_type: str | None = None
    event_location_type: str | None = None
    event_location: str | None = None
    street_address: str | None = None
    state: str | None = None
    state_initial: str | None = None
    zip_code: str | None = None
    country: str | None = None
    time_zone: str | None = None
    start_date: str | None = None
    start_time: str | None = None
    end_date: date|  None = None
    end_time: time | None = None
    logo: str | None = None
    industry: str | None = None
    team_name: str | None = None
    team_lead_email: str | None = None

    model_config = ConfigDict(from_attributes=True)

class EventCreate(EventBase):
    pass

class EventRead(EventBase):
    event_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class RecentEvent(BaseModel):
    event_name: str
    project_name: str

    model_config = ConfigDict(from_attributes=True)

class EventDetailUpdate(BaseModel):
    event_name: str | None = None
    event_type: str | None = None
    event_location_type: str | None = None
    event_location: str | None = None
    street_address: str | None = None
    state: str | None = None
    state_initial: str | None = None
    zip_code: str | None = None
    country: str | None = None
    time_zone: str | None = None
    start_date: str | None = None
    start_time: str | None = None
    end_date: date|  None = None
    end_time: time | None = None
    logo: str | None = None
    industry: str | None = None
    team_name: str | None = None
    team_lead_email: str | None = None

class EventOut(BaseModel):
    event_id: UUID
    event_name: str
    start_date: datetime
    start_time: datetime
    project_id: UUID

    model_config = ConfigDict(from_attributes=True)
    
# ===============================
# GuestTag Schemas
# ===============================

class GuestTagOptions(str, Enum):
    VIP = "VIP"
    Family = "Family"
    BusinessPartner = "Business partner"
    GuestSpeaker = "Guest speaker"
    OtherAttendees = "Other attendees"

class GuestTagBase(BaseModel):
    tag_name: GuestTagOptions
    event_id: UUID
    rsvp_form: bool = False
    name_badge_printing: bool = False

    model_config = ConfigDict(from_attributes=True)


class GuestTagCreate(GuestTagBase):
    broadcast_id: Optional[UUID] = None


class GuestTagRead(GuestTagBase):
    guest_tag_id: UUID
    created_at: datetime
    broadcast_id: UUID | None = None

    model_config = ConfigDict(from_attributes=True)

class GuestTagInfoResponse(BaseModel):
    event_id: UUID
    event_name: str
    total_guests: int
    total_checked_in: int

class GuestTagOverview(BaseModel):
    guest_tag_name: Optional[str]
    guest_tag_id: Optional[UUID]
    external_id: Optional[str]
    rsvp: bool
    checked_in: bool

# ===============================
# Guest Schemas
# ===============================

class GuestBase(BaseModel):
    salutation: str | None = None
    first_name: str
    last_name: str
    email: EmailStr
    phone: str | None = None
    seating_style: str | None = None
    color: str | None = None
    external_id: str | None = None
    affiliation: str | None = None
    secondary_email: EmailStr | None = None
    note: str | None = None
    rsvp: bool = False
    checked_in: bool = False
    guest_tag_id: UUID | None = None
    rsvp_form_id: UUID | None = None
    ai_form_id: UUID | None = None

    model_config = ConfigDict(from_attributes=True)


class GuestCreate(GuestBase):
    pass


class GuestRead(GuestBase):
    guest_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class GuestOverview(BaseModel):
    guest_name: str
    external_id: Optional[str]
    rsvp: bool
    checked_in: bool

class GuestTagOverviewResponse(BaseModel):
    event_id: UUID
    event_name: str
    guests: List[GuestOverview]

class AGuestOverview(BaseModel):
    salutation: Optional[str]
    first_name: str
    last_name: str
    email: str
    start_date: datetime
    seating_style: Optional[str]
    color: Optional[str]
    guest_list: Optional[str]
    external_id: Optional[str]
    affiliation: Optional[str]
    secondary_email: Optional[str]
    note: Optional[str]

class GuestSimpleCreate(BaseModel):
    salutation: Optional[str] = None
    first_name: str
    email: EmailStr
    phone: Optional[str] = None
    guest_tag_id: UUID

class GuestFormDetailOut(BaseModel):
    event_name: str
    event_description: Optional[str]
    email: str

    model_config = ConfigDict(from_attributes=True)

class GuestOut(BaseModel):
    guest_id: UUID
    salutation: Optional[str]
    first_name: str
    last_name: str
    email: str
    phone: Optional[str]
    seating_style: Optional[str]
    color: Optional[str]
    external_id: Optional[str]
    affiliation: Optional[str]
    secondary_email: Optional[str]
    note: Optional[str]
    rsvp: bool
    checked_in: bool

    model_config = ConfigDict(from_attributes=True)

# ===============================
# NotificationSetting Schemas
# ===============================

class NotificationSettingCreate(BaseModel):
    email_recipients: Optional[str] = None 
    text_recipients: Optional[str] = None
    guest_id: UUID

class NotificationSettingRead(NotificationSettingCreate):
    notification_setting_id: UUID

    model_config = ConfigDict(from_attributes=True)

# ===============================
# Vendor Schemas
# ===============================

class VendorBase(BaseModel):
    service_name: str = Field(..., max_length=200, description="Name or service offered by the vendor")
    email: EmailStr | None = None
    phone_number: str | None = None
    office_address: str | None = None
    star_rating: Optional[float] = Field(None, ge=0.0, le=5.0, description="Rating value between 0 and 5")
    total_ratings: Optional[int] = Field(None, ge=0, description="Total number of user ratings")
    last_opened: datetime | None = None
    user_id: UUID
    event_id: UUID

    model_config = ConfigDict(from_attributes=True)


class VendorCreate(VendorBase):
    pass


class VendorRead(VendorBase):
    vendor_id: UUID

    model_config = ConfigDict(from_attributes=True)

class VendorOut(BaseModel):
    service_name: str
    star_rating: Optional[float]
    total_ratings: Optional[int]
    office_address: Optional[str]
    email: Optional[EmailStr]
    phone_number: Optional[str]

    model_config = ConfigDict(from_attributes=True)

class EmailVendorRequest(BaseModel):
    vendor_email: EmailStr
    message: str

class TextVendorRequest(BaseModel):
    phone_number: str
    message: str

# ===============================
# Task Schemas
# ===============================

class TaskBase(BaseModel):
    task_name: str
    task_description: str| None = None
    start_date: date | None = None
    end_date: date | None = None
    task_instruction: str | None = None
    attachment_url: str | None = None
    maximum_team: int | None = None
    priority: str | None = None
    linked_to_event: bool = False
    event_id: UUID | None = None
    days_left: int | None = None
    progress_value: float | None = None
    task_status: str | None = None
    total_chats: int | None = None
    team_name: str | None = None
    team_lead_email: EmailStr | None = None
    team_member_images: str | None = None
    progress_description: str | None = None
    progress_status: str | None = None
    completion_percentage: float | None = None
    days_since_completion: int | None = None
    user_id: UUID

    model_config = ConfigDict(from_attributes=True)

class CreateTaskRequest(BaseModel):
    task_name: str
    task_description: str | None = None
    start_date: date | None = None
    end_date: date | None = None
    event_id: UUID

class CreateTaskResponse(BaseModel):
    task_id: UUID
    task_name: str
    task_description:  str | None = None
    start_date: date | None = None
    end_date: date | None = None


class TaskRead(TaskBase):
    task_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class RecentTask(BaseModel):
    project_name: str
    event_name: str | None = None
    task_name: str
    task_status: str | None = None
    days_left: int | None = None
    member_images: List[str] = Field(default_factory=list)
    total_chats: int | None = None

    model_config = ConfigDict(from_attributes=True)

class AddTaskDetailRequest(BaseModel):
    task_description: Optional[str] = None
    task_instruction: Optional[str] = None
    attachment_url: Optional[str] = None
    maximum_team: Optional[int] = None
    priority: Optional[str] = None
    linked_to_event: bool
    event_id: UUID

class AddTaskDetailResponse(BaseModel):
    task_id: UUID
    task_description: Optional[str]
    task_instruction: Optional[str]
    attachment_url: Optional[str]
    maximum_team: Optional[int]
    priority: Optional[str]
    linked_to_event: bool
    event_id: UUID
    
class TeamMemberAssignment(BaseModel):
    email: EmailStr
    role: str

class AttachTeamMembersRequest(BaseModel):
    members: Union[TeamMemberAssignment, List[TeamMemberAssignment]]

class AttachTeamMembersResponse(BaseModel):
    task_id: UUID
    team_id: UUID
    added_members: List[EmailStr]

class TaskOverviewResponse(BaseModel):
    task_id: UUID
    task_name: str
    event_name: str
    days_left: Optional[int]
    progress_value: Optional[float]
    team_member_images: List[str]
    task_status: Optional[str]
    total_chats: int

class TaskDetailResponse(BaseModel):
    task_id: UUID
    task_name: str
    priority: Optional[str]
    start_date: Optional[date]
    end_date: Optional[date]
    team_member_images: List[str]
    team_name: Optional[str]
    task_status: Optional[str]
    team_lead_email: Optional[str]
    task_description: Optional[str]

class TaskProgressResponse(BaseModel):
    task_id: UUID
    progress_description: Optional[str]
    progress_status: Optional[str]

class ChatMessageInfo(BaseModel):
    sender_name: str
    sender_image: Optional[str]
    message: str
    attachment: Optional[str]
    timestamp: str  # formatted date string

class TaskTeamChatResponse(BaseModel):
    team_name: str
    team_member_images: List[str]
    messages: List[ChatMessageInfo]

class CompleteTaskResponse(BaseModel):
    task_name: str
    event_name: str
    completion_percentage: Optional[float]
    task_status: Optional[str]
    days_since_completion: Optional[int]
    team_images: List[str]
    team_name: Optional[str]
    total_chats: int

class TaskTeamMemberResponse(BaseModel):
    full_name: str
    email: str
    role: str
    status: str
     
# ===============================
# Team Schemas
# ===============================

class TeamBase(BaseModel):
    team_name: str = Field(..., max_length=100, description="Name of the team")
    event_id: Optional[UUID] = Field(None, description="Associated event ID if team is linked to an event")

    model_config = ConfigDict(from_attributes=True)

class TeamCreate(TeamBase):
    pass

class TeamResponse(BaseModel):
    team_id: UUID
    team_name: str
    event_id: UUID

class TeamRead(TeamBase):
    team_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class TeamLeadEvent(BaseModel):
    image: str | None
    email: str
    team_name: str
    project_name: str
    event_name: str

    model_config = ConfigDict(from_attributes=True)

class TeamMemberCreate(BaseModel):
    member_name: str
    email: EmailStr
    role: str
    description: Optional[str] = None
    team_id: UUID

class TeamMemberResponse(BaseModel):
    team_member_id: UUID
    member_name: str
    email: EmailStr
    role: str
    description: Optional[str]
    team_id: UUID

class TeamMemberOverview(BaseModel):
    image: Optional[str]
    member_name: str
    skill: Optional[str]
    role: str
    date_added: datetime
    email: EmailStr

class TeamMembersOverviewResponse(BaseModel):
    members: List[TeamMemberOverview]

class TeamAnalysisResponse(BaseModel):
    total_teams: int
    total_leads: int
    total_members: int
    total_tasks: int

class SingleTeamAnalysisResponse(BaseModel):
    team_name: str
    total_leads: int
    total_members: int
    member_images: List[str]

# ===============================
# TeamMember Schemas
# ===============================

class TeamMemberBase(BaseModel):
    team_id: UUID
    member_name: str = Field(..., max_length=100)
    email: EmailStr
    role: str = Field(..., max_length=100)
    description: Optional[str] = Field(None, max_length=250)
    image: Optional[str] = Field(None, max_length=250)
    skill: Optional[str] = Field(None, max_length=100)

    model_config = ConfigDict(from_attributes=True)

class TeamMemberCreate(TeamMemberBase):
    pass


class TeamMemberRead(TeamMemberBase):
    team_member_id: UUID
    date_added: datetime

    model_config = ConfigDict(from_attributes=True)

# ===============================
# RSVPForm Schemas
# ===============================

class RSVPFormBase(BaseModel):
    guest_tag_id: UUID
    rsvp_title: str = Field(..., max_length=200, description="Main title displayed on the RSVP form")
    rsvp_sub_message: Optional[str] = Field(None, max_length=250, description="Optional subheading or message")
    event_logo: Optional[str] = Field(None, max_length=250, description="URL to event logo or branding image")
    button_type: str = Field(..., max_length=50, description="Type of button (e.g., 'submit', 'confirm')")
    sender_email: EmailStr
    sender_reply_email: EmailStr
    responsive: bool = True

    model_config = ConfigDict(from_attributes=True)

class RSVPFormCreate(BaseModel):
    guest_tag_id: UUID
    rsvp_title: str
    rsvp_sub_message: Optional[str] = None
    event_logo: Optional[str] = None
    button_type: str
    sender_email: EmailStr
    sender_reply_email: EmailStr
    responsive: bool

class RSVPFormRead(RSVPFormBase):
    rsvp_form_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class GuestTagRSVPStatus(BaseModel):
    guest_tag_name: str
    event_name: str
    total_guests: int
    rsvp_status: str  # "configured" or "not-configured"

class ConfiguredRSVPFormOut(BaseModel):
    guest_tag_name: str
    event_name: str
    total_guests: int
    rsvp_status: str
# ===============================
# ChatSession Schemas
# ===============================

class ChatSessionBase(BaseModel):
    task_id: UUID
    session_name: Optional[str] = Field(None, max_length=100, description="Optional name of the chat session")

    model_config = ConfigDict(from_attributes=True)

class ChatSessionCreate(ChatSessionBase):
    pass

class ChatSessionRead(ChatSessionBase):
    session_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class TeamChatSummary(BaseModel):
    chat_session_id: UUID
    session_name: str | None
    member_images: List[str] = Field(default_factory=list)
    total_leads: int
    total_members: int

    model_config = ConfigDict(from_attributes=True)

class UserProfileOut(BaseModel):
    full_name: str
    email: EmailStr
    image: str

class ChatMessageOut(BaseModel):
    sender_id: UUID
    message: Optional[str]
    attachment: Optional[str]
    media: Optional[str]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class ChatSessionWithMessages(BaseModel):
    chat_session_id: UUID
    session_name: Optional[str]
    created_at: datetime
    task_name: str
    messages: List[ChatMessageOut]

    model_config = ConfigDict(from_attributes=True) 

class TaskChatMemberImage(BaseModel):
    image: Optional[str]

    model_config = ConfigDict(from_attributes=True)

class TaskChatSummary(BaseModel):
    task_name: str
    team_name: str
    member_images: List[TaskChatMemberImage]

    model_config = ConfigDict(from_attributes=True)

class MemberImage(BaseModel):
    image: Optional[str]

    model_config = ConfigDict(from_attributes=True)

class TeamMessageDetail(BaseModel):
    team_name: str
    member_images: List[MemberImage]

    model_config = ConfigDict(from_attributes=True)

class MessageDetail(BaseModel):
    sender_name: str
    sender_image: Optional[str]
    day_time: str
    message: Optional[str]
    attachment: Optional[str]
    media: Optional[str]

    model_config = ConfigDict(from_attributes=True)

class ChatSessionMessages(BaseModel):
    team_name: str
    member_images: List[Optional[str]]
    messages: List[MessageDetail]

    model_config = ConfigDict(from_attributes=True)

# ===============================
# ChatMessage Schemas
# ===============================

class ChatMessageBase(BaseModel):
    session_id: UUID
    sender_id: UUID
    message: Optional[str] = Field(None, description="Text content of the message")
    attachment: Optional[str] = Field(None, description="URL to an attached file")
    media: Optional[str] = Field(None, description="URL to a media file (image, video, etc.)")

    model_config = ConfigDict(from_attributes=True)

class SendMessageCreate(BaseModel):
    team_id: UUID
    task_id: UUID
    chat_session_id: UUID
    sender_id: UUID
    message: Optional[str] = None
    attachment: Optional[str] = None
    media: Optional[str] = None

class ChatMessageCreate(ChatMessageBase):
    pass

class ChatMessageRead(ChatMessageBase):
    message_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# ===============================
# Broadcast Schemas
# ===============================

class BroadcastBase(BaseModel):
    event_id: UUID
    message_subject: str = Field(..., max_length=200)
    message: str
    medium: str = Field(..., description="Broadcast medium: email, whatsapp, or sms")
    status: str = Field(default="not-sent", description="Broadcast delivery status")

    model_config = ConfigDict(from_attributes=True)

class BroadcastCreate(BaseModel):
    event_id: UUID
    guest_tag_ids: List[UUID]
    message_subject: str
    message: str
    medium: Literal["text", "email", "whatsapp"]


class BroadcastRead(BroadcastBase):
    broadcast_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

class GuestTagBroadcastOut(BaseModel):
    tag_name: str
    event_name: str
    total_guests: int
    message: str
    status: str

    model_config = ConfigDict(from_attributes=True)
# ===============================
# AIForm Schemas
# ===============================

class AIFormBase(BaseModel):
    form_title: str = Field(..., max_length=200, description="Title of the AI Feedback form")
    event_id: UUID
    share_link: Optional[str] = Field(None, max_length=250, description="Optional shareable form link")
    notification_id: Optional[UUID] = None

    model_config = ConfigDict(from_attributes=True)

class AIFormCreate(BaseModel):
    form_title: str
    event_id: UUID

class EditAIFormRequest(BaseModel):
    form_title: str

class AIFormRead(AIFormBase):
    ai_form_id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# ===============================
# FeedbackQuestion Schemas
# ===============================

class FeedbackQuestionBase(BaseModel):
    ai_form_id: UUID
    question_initial: str = Field(..., max_length=100, description="Short code or label for the question")
    question: str = Field(..., description="The full feedback question")

    model_config = ConfigDict(from_attributes=True)

class QuestionInput(BaseModel):
    question_initial: str
    question: str

class CreateFeedbackRequest(BaseModel):
    questions: List[QuestionInput]
    
class FeedbackQuestionCreate(FeedbackQuestionBase):
    pass


class FeedbackQuestionRead(FeedbackQuestionBase):
    feedback_question_id: UUID
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# ===============================
# AIFormTheme Schemas
# ===============================

class AIFormThemeBase(BaseModel):
    ai_form_id: UUID
    theme_color: Optional[str] = Field(None, max_length=50)
    brand_logo: Optional[str] = Field(None, max_length=250)
    background_image: Optional[str] = Field(None, max_length=250)
    layout_color: Optional[str] = Field(None, max_length=50)
    text_color: Optional[str] = Field(None, max_length=50)
    button_color: Optional[str] = Field(None, max_length=50)
    button_text_color: Optional[str] = Field(None, max_length=50)
    background_color: Optional[str] = Field(None, max_length=50)
    type_face: Optional[str] = Field(None, max_length=50)
    text_animation: Optional[str] = Field(None, max_length=50)
    button_radius: Optional[float] = None
    background_opacity: Optional[float] = None
    logo: Optional[str] = Field(None, max_length=250)

    model_config = ConfigDict(from_attributes=True)

class AIFormThemeCreate(AIFormThemeBase):
    pass

class AIFormThemeRead(AIFormThemeBase):
    theme_id: UUID

    model_config = ConfigDict(from_attributes=True)

class ThemeCustomizeRequest(BaseModel):
    theme_color: Optional[str] = None
    brand_logo: Optional[str] = None
    background_image: Optional[str] = None
    layout_color: Optional[str] = None
    text_color: Optional[str] = None
    button_color: Optional[str] = None
    button_text_color: Optional[str] = None
    background_color: Optional[str] = None
    type_face: Optional[str] = None
    text_animation: Optional[str] = None
    button_radius: Optional[float] = None
    background_opacity: Optional[float] = None
    logo: Optional[str] = None

# ===============================
# AIFormResponseLogic Schemas
# ===============================

class AIFormResponseLogicBase(BaseModel):
    ai_form_id: UUID
    email_logic: bool = Field(False, description="Whether email logic is enabled for the form")
    email_subject: Optional[str] = Field(None, max_length=200)
    sender_name: Optional[str] = Field(None, max_length=100)
    sender_email: Optional[EmailStr] = None
    reply_email: Optional[EmailStr] = None

    model_config = ConfigDict(from_attributes=True)

class AIFormResponseLogicCreate(AIFormResponseLogicBase):
    pass

class AIFormResponseLogicRead(AIFormResponseLogicBase):
    response_logic_id: UUID

    model_config = ConfigDict(from_attributes=True)

class AIFormResponseLogicRequest(BaseModel):
    email_logic: bool
    email_subject: Optional[str] = None
    sender_name: Optional[str] = None
    sender_email: Optional[EmailStr] = None
    reply_email: Optional[EmailStr] = None

# ===============================
# Domain Schemas
# ===============================

class DomainBase(BaseModel):
    timeless_domain: bool = Field(False, description="Is this a system-provided domain?")
    company_name: str = Field(..., max_length=250, description="Company name associated with the domain")
    custom_domain: bool = Field(False, description="Indicates if a custom domain is used")
    custom_domain_name: Optional[str] = Field(None, max_length=250, description="Custom domain name (if any)")
    verification_status: str = Field("not verified", max_length=50, description="Verification status of the domain")
    type: Optional[str] = Field(None, max_length=50, description="DNS record type (e.g., CNAME, TXT)")
    name: Optional[str] = Field(None, max_length=250, description="DNS record name")
    value: Optional[str] = Field(None, max_length=250, description="DNS record value")
    user_id: int

    model_config = ConfigDict(from_attributes=True)

class DomainCreate(DomainBase):
    pass


class DomainRead(DomainBase):
    domain_id: UUID

    model_config = ConfigDict(from_attributes=True)

# ===============================
# Notification Schemas
# ===============================

class NotificationBase(BaseModel):
    triggered_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp when the notification was triggered")
    message_heading: str = Field(..., max_length=250, description="Heading or title of the notification")
    message_body: str = Field(..., description="Full content of the notification")

    model_config = ConfigDict(from_attributes=True)

class NotificationCreate(NotificationBase):
    pass

class NotificationRead(NotificationBase):
    notification_id: UUID

    model_config = ConfigDict(from_attributes=True)


# ===============================
# NotificationPreference Schemas
# ===============================

# ---- Enums for constrained choices (better than strings) ----
class NotificationMethod(str, Enum):
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push_notification"

class Frequency(str, Enum):
    INSTANT = "instant"
    DAILY = "daily"
    WEEKLY = "weekly"

# ---- Core Schema ----
class NotificationPreferenceBase(BaseModel):
    model_config = ConfigDict(from_attributes=True)  # Enables ORM mode (formerly `orm_mode=True`)

    user_id: UUID
    general_notification: bool = True
    task_completion: bool = True
    event_creation_and_completion: bool = True
    system_alert: bool = True
    notification_method: NotificationMethod = NotificationMethod.EMAIL
    frequency: Frequency = Frequency.INSTANT

# ---- Create/Update Schemas (Optional but Recommended) ----
class NotificationPreferenceCreate(NotificationPreferenceBase):
    pass  # Add creation-specific validations if needed

class NotificationPreferenceUpdate(BaseModel):
    general_notification: bool | None = None
    task_completion: bool | None = None
    event_creation_and_completion: bool | None = None
    system_alert: bool | None = None
    notification_method: NotificationMethod | None = None
    frequency: Frequency | None = None

# ---- Response Schema (Optional) ----
class NotificationPreferenceResponse(NotificationPreferenceBase):
    preference_id: UUID
    # Example of computed field (if needed):
    # @computed_field
    # def summary(self) -> str:
    #     return f"{self.notification_method} at {self.frequency} frequency"

# ---- Validators (Example) ----
    @field_validator("notification_method")
    def validate_method(cls, v: NotificationMethod) -> NotificationMethod:
        if v == "sms":
            print("Warning: SMS may incur charges")  # Example business logic
        return v
    
# ===============================
# Workspace Schemas
# ===============================

class WorkspaceBase(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,  # Enables ORM mode (replaces `orm_mode=True`)
        str_strip_whitespace=True,  # Auto-trim string whitespace
    )

    workspace_name: str = Field(..., max_length=200)  # Matches SQL String(200)
    user_id: UUID

# ---- Create/Update Schemas ----
class WorkspaceCreate(WorkspaceBase):
    pass  

class WorkspaceUpdate(BaseModel):
    workspace_name: Optional[str] = Field(None, max_length=200)
    user_id: Optional[UUID] = None

# ---- Response Schema ----
class WorkspaceResponse(WorkspaceBase):
    workspace_id: UUID
    created_at: datetime

# ---- Validators (Example) ----
    @field_validator("workspace_name")
    def validate_name(cls, v: str) -> str:
        if len(v.strip()) < 3:
            raise ValueError("Name must be at least 3 characters")
        return v.title()  # Auto-capitalize

class WorkspaceCreateWithEmail(BaseModel):
    workspace_name: str = Field(..., max_length=200)
    email: EmailStr  # validated email input

    model_config = ConfigDict(from_attributes=True)

# ===============================
# BillingPlan Schemas
# ===============================

# ---- Enums for Status/Payment Method (Better than free-text) ----
class BillingStatus(str, Enum):
    ACTIVE = "active"
    CANCELED = "canceled"
    TRIAL = "trial"
    PAUSED = "paused"

class PaymentMethod(str, Enum):
    VISA = "visa"
    MASTERCARD = "mastercard"
    PAYPAL = "paypal"
    BANK_TRANSFER = "bank_transfer"

# ---- Core Schema ----
class BillingPlanBase(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,  # ORM mode
        str_strip_whitespace=True,  # Auto-trim strings
        str_to_lower=True,  # Normalize enums (e.g., "Visa" → "visa")
    )

    user_email: str = Field(..., max_length=250)
    plan_name: str = Field(..., max_length=100)
    amount_paid: float = Field(..., ge=0)  # Must be >= 0
    status: BillingStatus = BillingStatus.ACTIVE
    initial_plan: Optional[str] = Field(None, max_length=100)
    payment_method: Optional[PaymentMethod] = None
    billing_contact: Optional[str] = Field(None, max_length=250)
    billing_email: str = Field(..., max_length=250)

# ---- Create/Update Schemas ----
class BillingPlanCreate(BillingPlanBase):
    pass  # Add create-specific validations if needed

class BillingPlanUpdate(BaseModel):
    plan_name: Optional[str] = Field(None, max_length=100)
    amount_paid: Optional[float] = Field(None, ge=0)
    status: Optional[BillingStatus] = None
    payment_method: Optional[PaymentMethod] = None
    billing_contact: Optional[str] = Field(None, max_length=250)
    billing_email: Optional[str] = Field(None, max_length=250)

# ---- Response Schema ----
class BillingPlanResponse(BillingPlanBase):
    billing_plan_id: UUID
    created_at: datetime
    updated_at: datetime
    # Uncomment if you want to include user data:
    # user: List["UserResponse"]  # Requires forward reference

# ---- Validators ----
    @field_validator("billing_email", "user_email")
    def validate_email_format(cls, v: str) -> str:
        if "@" not in v:
            raise ValueError("Invalid email format")
        return v.lower()  # Normalize to lowercase

    @field_validator("amount_paid")
    def round_amount(cls, v: float) -> float:
        return round(v, 2)  # Ensure 2 decimal places

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone_number: Optional[str] = None
    job_title: Optional[str] = None
    old_password: Optional[str] = None
    new_password: Optional[str] = None

class CancelBillingRequest(BaseModel):
    user_email: EmailStr
    plan_name: str

class UpgradePlanRequest(BaseModel):
    user_email: EmailStr
    current_plan: str = Field(..., max_length=100)
    initial_plan: str = Field(..., max_length=100)
    initial_amount: float = Field(..., ge=0)
    current_amount: float = Field(..., ge=0)

class AddPaymentMethodRequest(BaseModel):
    user_email: EmailStr
    payment_method: PaymentMethod   
# ===============================
# CRMIntegration Schemas
# ===============================

# ---- Enums for Known Providers (Optional but Recommended) ----
class CRMProvider(str, Enum):
    SALESFORCE = "salesforce"
    HUBSPOT = "hubspot"
    ZOHO = "zoho"
    PIPEDRIVE = "pipedrive"
    CUSTOM = "custom"

# ---- Core Schema ----
class CRMIntegrationBase(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,  # ORM mode
        str_strip_whitespace=True,
        str_to_lower=True  # Normalize provider names
    )

    provider_name: CRMProvider | str = Field(..., max_length=100)  # Accepts enum or custom string
    url_link: HttpUrl = Field(..., max_length=250)  # Or use HttpUrl for strict URL validation
    user_id: UUID | None = None

# ---- Create/Update Schemas ----
class CRMIntegrationCreate(CRMIntegrationBase):
    pass  

class CRMIntegrationUpdate(BaseModel):
    provider_name: Optional[CRMProvider | str] = Field(None, max_length=100)
    url_link: Optional[str] = Field(None, max_length=250)
    user_id: UUID | None = None

# ---- Response Schema ----
class CRMIntegrationResponse(CRMIntegrationBase):
    crm_integration_id: UUID
    integrated_at: datetime
    # Uncomment if you want to include nested user data:
    # user: Optional["UserResponse"]  # Requires forward reference

# ---- Validators ----
    @field_validator("url_link")
    def validate_url(cls, v: str) -> str:
        if not v.startswith(("http://", "https://")):
            raise ValueError("URL must start with http:// or https://")
        return v.strip()

    @field_validator("provider_name")
    def normalize_provider(cls, v: str | CRMProvider) -> str:
        if isinstance(v, CRMProvider):
            return v.value
        return v.lower().replace(" ", "_")  # Normalize to snake_case