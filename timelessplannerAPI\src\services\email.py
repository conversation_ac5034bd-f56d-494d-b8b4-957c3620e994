from src.utils.commonImports import *
import ssl
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging
from src.utils.config import settings
import time

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class EmailService:
    @staticmethod
    async def send_email(subject: str, recipients: Union[str, List[str]],body:Optional[str] = None, html_body: Optional[str] = None, max_retries=3):
        """
        Sends an email using the configured SMTP server.
    
        :param subject: Email subject
        :param recipients: A single email or a list of email addresses
        :param body: Email body content
        :param max_retries: Maximum retry attempts for sending the email
        """
        if not body and not html_body:
            raise ValueError("Either 'html_body' or 'text_body' must be provided")
        
        # Handle single or multiple recipients
        if isinstance(recipients, list):
            if not recipients:
                raise ValueError("Recipients list is empty")
            recipients_str = ", ".join(recipients)
        elif isinstance(recipients, str):
            recipients_str = recipients
        else:
            raise TypeError("Recipients must be a string or a list of strings")

        # Prepare the email message
        msg = MIMEMultipart("alternative")
        msg.set_unixfrom('author')
        msg['From'] = f"{settings.MAIL_FROM}"
        msg['To'] = recipients_str
        msg['Subject'] = subject

        # Attach plain text and/or HTML body
        if body:
            msg.attach(MIMEText(body, "plain"))
        if html_body:
            msg.attach(MIMEText(html_body, "html"))

        # Try sending the email with retries
        for attempt in range(max_retries):
            try:
                with smtplib.SMTP('smtpout.secureserver.net', 587) as mailserver:
                    mailserver.ehlo()
                    mailserver.starttls()
                    mailserver.login(settings.MAIL_USERNAME, settings.MAIL_PASSWORD)
                    mailserver.sendmail(settings.MAIL_USERNAME, recipients if isinstance(recipients, list) else [recipients], msg.as_string())
                    logger.info("Email sent successfully.")
                return  # Exit function on success

            except smtplib.SMTPException as e:
                logger.error(f"Attempt {attempt + 1} - Failed to send email: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    logger.error(f"Email could not be sent after {max_retries} attempts. Error: {str(e)}")
                    raise

    @staticmethod
    async def send_invite_email(recipients: str, invite_link: str):
        subject = "You Are Invited!"
        body = f"Please use the following link to join: {invite_link}"
        return await EmailService.send_email(subject, recipients, body)

    @staticmethod
    async def send_reminder_email(subject:str,recipients: str, reminder_message: str):
        body = f"{reminder_message}"
        return await EmailService.send_email(subject, recipients, body)

    @staticmethod
    async def send_otp_email(recipients: str, otp_code: str):
        subject = "Your OTP Code"
        body = f"Your OTP code is {otp_code}. Please use this to complete your verification on timelessplanner.com."
        return await EmailService.send_email(subject, recipients, body)
