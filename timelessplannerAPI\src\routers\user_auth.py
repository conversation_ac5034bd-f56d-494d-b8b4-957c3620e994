import random
import re
from pydantic import  EmailStr
from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.utils.config import settings
from src.services.oauth import <PERSON><PERSON>uth,GoogleOAuth
from fastapi.responses import JSONResponse
from src.schemas import schemas
from src.models import models
from src.utils import auth,config
from src.services.email_service_smtp import EmailService, SMTPMailer
from src import crud
import secrets


router = APIRouter(prefix="/userAuthentication", tags=["userAuthentication"])

#handle logging reports
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Utility function to generate a 6-digit OTP
def generate_otp():
    return str(secrets.randbelow(9000) + 1000)

# Endpoint to sign up user
@router.post("/Signup",response_model=schemas.UserOut)
async def register_user(
    user_in: schemas.UserCreate, 
    db: AsyncSession = Depends(get_session)
    ):

    # Hash user submitted password
    hashed_password = auth.get_hashed_password(user_in.password)
    
    # Create the user in the database
    created_user = await crud.users.create_user(db, user_in, hashed_password)

    # Generate a 6-digit OTP
    otp_code = generate_otp()
    
    # Create OTP data for saving in the database
    user_otp_create = schemas.UserOTPCreate(
        user_id=created_user.user_id,
        email=created_user.email,
        otp_code=otp_code,
        is_used=False  
        )

    # Save the OTP in the database
    await crud.otp.create_user_otp(db, user_otp_create)
    await EmailService.send_otp_email(user_in.email, otp_code)
    return created_user

@router.post("/verifyOtp")
async def verify_otp(
    email: EmailStr, 
    otp_code: str, 
    db: AsyncSession = Depends(get_session)
):
    # Fetch the OTP details
    user_otp = await crud.otp.get_user_otp_by_email(db, email, otp_code)
    
    if user_otp is None:
        raise HTTPException(status_code=400, detail="Invalid OTP or OTP does not exist.")
    
    if user_otp.is_used:
        raise HTTPException(status_code=400, detail="OTP has already been used.")

    # Check if OTP is expired
    if hasattr(user_otp, "expires_at") and user_otp.expires_at < datetime.now(timezone.utc):
        raise HTTPException(status_code=400, detail="OTP has expired.")

    # Mark OTP as used
    await crud.otp.update_user_otp(db, user_otp.otp_id, schemas.UserOTPUpdate(is_used=True))
    
    # Fetch and activate the user
    user = await crud.users.get_user_by_email(db, email)
    if not user:
        raise HTTPException(status_code=404, detail="User with email address not found.")
    
    success = await crud.users.activate_user_by_email(db, email)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to activate user account.")
    
    return JSONResponse(content={"message": "Account verification successful."})

# Endpoint to resend user OTP
@router.post("/sendOtp", response_model=None)
async def sendOtp(
    email:str,
    db: AsyncSession = Depends(get_session)
    ):
    new_otp_code = generate_otp()

    # Fetch the user using the user email address
    user = await crud.users.get_user_by_email(db,email)
    if not user:
        raise HTTPException(status_code=404, detail="User with email address not found.")

    # Create OTP data for saving in the database
    user_otp_create = schemas.UserOTPCreate(
        user_id=user.user_id,
        email=email,
        otp_code=new_otp_code,
        is_used=False  
        )

    create_new_otp = await crud.otp.create_user_otp(db,user_otp_create)
    if create_new_otp is None:
        raise HTTPException(status_code=400, detail="Invalid OTP or OTP does not exist.")
    otp_response = await EmailService.send_otp_email(email,new_otp_code)   
    return {
        "status":"email otp sent successfully"
    }

@router.post("/authenticate", summary="Create access and refresh tokens for user", response_model=schemas.TokenOut)
async def authenticate(payload: OAuth2PasswordRequestForm = Depends(), db: AsyncSession = Depends(get_session)):
    logger.info(f"Login attempt from email: {payload.username}")

    try:
        user = await crud.users.get_user_by_email(db=db, email=payload.username)
        if user is None:
            logger.warning(f"Login failed: User not found for email {payload.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
            )

        # Check if the user is active
        if not user.is_active:
            logger.warning(f"Login failed: Inactive account for email {user.email}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is inactive. Please verify your email to activate your account.",
            )

        if not auth.verify_password(payload.password, user.password):
            logger.warning(f"Login failed: Invalid password for user {user.email}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
            )

        # Create access and refresh tokens
        access_token = auth.create_access_token(subject=str(user.user_id))
        refresh_token = auth.create_refresh_token(subject=str(user.user_id))

        # Create and save token data
        token_db = models.Token(
            user_id=user.user_id,
            access_token=access_token,
            refresh_token=refresh_token,
            status=True
        )
        db.add(token_db)
        await db.commit()
        await db.refresh(token_db)

        logger.info(f"Login successful for user: {user.email}")
        
        return schemas.TokenOut(
            token_id=token_db.token_id,
            user_id=user.user_id,
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            status=token_db.status,
            created_at=token_db.created_at,
            exp=int((token_db.created_at + timedelta(days=30)).timestamp())
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error during login for email {payload.username}: {str(e)}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")

@router.post("/refreshToken", summary="Refresh access token")
async def refresh_token(request: schemas.TokenRefreshRequest, db: AsyncSession = Depends(get_session)):
    try:
        logger.debug(f"Refresh token received: {request.refresh_token}")

        payload = jwt.decode(
            request.refresh_token,
            config.settings.JWT_REFRESH_SECRET_KEY.get_secret_value(),
            algorithms=[config.settings.JWT_ALGORITHM]
        )
        user_id = payload.get("sub")

        if not user_id:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")

        # Verify if the user exists and is active
        try:
            user = await crud.users.get_user(db=db, user_id=user_id)
        except Exception as db_err:
            logger.error("Database error while fetching user: %s\n%s", db_err, traceback.format_exc())
            raise HTTPException(status_code=500, detail="Database error")

        if user is None or not user.is_active:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found or inactive")

        # Create a new access token
        new_access_token = auth.create_access_token(subject=user_id)
        return {"access_token": new_access_token, "token_type": "bearer"}

    except ExpiredSignatureError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired")
    except InvalidTokenError:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token")
    except HTTPException as http_exc:
        raise http_exc  # Let FastAPI handle known exceptions
    except Exception as e:
        logger.error("Unexpected error in refresh_token: %s\n%s", e, traceback.format_exc())
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An unexpected error occurred")
    
@router.post("/sendPasswordResetLink", status_code=status.HTTP_200_OK)
async def send_reset_link(
    request: schemas.PasswordResetRequest,
    db: AsyncSession = Depends(get_session),
):
    msg = {
        "message": "If an account with that email exists, you will receive a reset link shortly."
    }

    # 1) lookup user
    user = await crud.users.get_user_by_email(db, request.email)
    if not user:
        return msg  # don't reveal if email exists

    # 2) build JWT reset token (1 hr expiry)
    now = datetime.now(timezone.utc)
    expire_at = now + timedelta(hours=1)
    payload = {"sub": str(user.user_id), "exp": expire_at}
    token = jwt.encode(
        payload,
        settings.JWT_SECRET_KEY.get_secret_value(),
        algorithm=settings.JWT_ALGORITHM
    )

    # 3) persist token in DB
    reset_token_in = schemas.PasswordResetTokenCreate(
        user_id=user.user_id,
        token=token,
        expires_at=expire_at,
    )
    await crud.reset_tokens.create_reset_token(db, reset_token_in)

    # 4) send email
    reset_link = f"{settings.FRONTEND_URL}/reset-password?token={token}"
    await EmailService.send_password_reset_email(request.email, reset_link)

    return reset_link

@router.post("/verifyResetLink", response_model=schemas.UserOut)
async def verify_reset_link(
    request: schemas.VerifyResetLinkRequest,
    db: AsyncSession = Depends(get_session),
):
    """
    1) Decode JWT (signature + expiry).
    2) Ensure a matching, unused token exists in DB.
    3) Ensure the user exists.
    4) Mark token as used.
    Returns 200 {"valid": True} if OK; raises 401/404 on failure.
    """
    # 1) Decode & validate signature + expiry
    try:
        payload = jwt.decode(
            request.token,
            settings.JWT_SECRET_KEY.get_secret_value(),
            algorithms=[settings.JWT_ALGORITHM],
        )
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Reset link has expired."
        )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid reset link."
        )

    # 2) Confirm token exists, not expired, not used
    token_record = await crud.reset_tokens.get_valid_token(db, request.token)
    if not token_record:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or already used reset link."
        )

    # 3) Confirm the user actually exists
    user = await crud.users.get_user(db, UUID(payload.get("sub", 0)))
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found."
        )
    # Ensure the user is active 
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is inactive. Please verify your email to activate your account."
        )

    # 4) Mark this reset token as used so it cannot be reused
    await crud.reset_tokens.mark_token_as_used(db, token_record.token)


    return user

#endpoint to reset password
@router.post('/restPassword', response_model=None)
async def reset_password(
    request: schemas.ChangePassword,
    db: AsyncSession = Depends(get_session)
    ):
    try:
        # Fetch the user from the database to get the latest data
        logger.info(f"Fetching user with ID: {request.email}")
        user_result = await db.execute(select(models.User).where(models.User.email == request.email))
        user = user_result.scalars().first()

        if not user:
            logger.error(f"User with ID {request.email} not found in the database")
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Could not find user")
        
        # Update password
        user.password = auth.get_hashed_password(request.new_password)
        db.add(user)  
        await db.commit()

        logger.info(f"User: {user.email} successfully changed password")
        return {"message": "Password changed successfully"}

    except Exception as e:
        logger.error(f"Error changing password: {e}")
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error changing password")

#endpoint to initiate google authentication
@router.get("/auth/google")
async def google_login(response: Response):
    """Initiate Google OAuth flow"""
    state = GoogleOAuth.create_oauth_state(response)
    
    auth_url = (
        f"{config.settings.GOOGLE_AUTH_URL}?"
        f"response_type=code&"
        f"client_id={config.settings.GOOGLE_CLIENT_ID}&"
        f"scope=openid%20email%20profile&"
        f"redirect_uri={config.settings.GOOGLE_REDIRECT_URI}&"
        f"state={state}&"
        f"access_type=offline"
    )
    
    return RedirectResponse(auth_url)

#endpoint to handle google authentication
@router.get("/auth/google/callback")
async def google_callback(
    request: Request,
    code: str,
    state: Optional[str] = None,
    db: AsyncSession = Depends(get_session)
):
    """Handle Google OAuth callback"""
    try:
        # Validate state parameter
        stored_state = request.cookies.get("oauth_state")
        if not state or state != stored_state:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid state parameter"
            )

        # Exchange code for user data
        user_info = await GoogleOAuth.exchange_code(code)
        
        # Validate required fields
        if not all([user_info.get("email"), user_info.get("sub")]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Incomplete user data from Google"
            )

        # Atomic user creation/update
        async with db.begin():
            user = await crud.users.get_user_by_email(db, user_info["email"])
            
            if not user:
                user = models.User(
                    email=user_info["email"],
                    full_name=user_info.get("name", ""),
                    provider="google",
                    provider_id=user_info["sub"],
                    user_image=user_info.get("picture"),
                    is_active=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.add(user)
                await db.commit()
                await db.refresh(user)
            else:
                # Update existing user
                user.full_name = user_info.get("name", user.full_name)
                user.avatar_url = user_info.get("picture", user.avatar_url)
                user.updated_at = datetime.utcnow()
                await db.commit()

        # Generate secure JWT
        jwt_token = GoogleOAuth.create_secure_jwt(str(user.user_id))
        
        # Secure response with HTTP-only cookie
        response = JSONResponse(
            content={
                "user_id": str(user.id),
                "email": user.email,
                "name": user.full_name
            }
        )
        response.set_cookie(
            key="access_token",
            value=jwt_token,
            httponly=True,
            secure=config.settings.ENV == "production",
            samesite="lax",
            max_age=config.settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
        return response

    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User already exists"
        )
        
    except HTTPException as e:
        raise e
        
    except Exception as e:
        # Log full error for debugging
        print(f"Google OAuth error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed"
        )


# Endpoint to initiate Facebook authentication
@router.get("/auth/facebook")
async def facebook_login(response: Response):
    """Initiate Facebook OAuth flow"""
    state = create_oauth_state(response)
    
    auth_url = (
        f"{FacebookOAuth.get_oauth_config()['auth_url']}?"
        f"client_id={config.settings.FACEBOOK_CLIENT_ID}&"
        f"redirect_uri={config.settings.FACEBOOK_REDIRECT_URI}&"
        f"state={state}&"
        "response_type=code&"
        "scope=email,public_profile"
    )
    
    return RedirectResponse(auth_url)

@router.get("/auth/facebook/callback")
async def facebook_callback(
    request: Request,
    code: str,
    state: Optional[str] = None,
    db: AsyncSession = Depends(get_session)
):
    """Handle Facebook OAuth callback"""
    try:
        # Validate state parameter
        stored_state = request.cookies.get("oauth_state")
        if not state or state != stored_state:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid state parameter"
            )

        # Exchange code for user data
        user_info = await FacebookOAuth.exchange_code(code)
        
        # Validate required fields
        if not user_info.get("id"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid Facebook user data"
            )
            
        if not user_info.get("email"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Email permission required for Facebook login"
            )

        # Atomic user creation/update
        async with db.begin():
            user = await crud.users.get_user_by_email(
                db,email=user_info["email"]
            )
            
            if not user:
                user = models.User(
                    email=user_info["email"],
                    full_name=user_info.get("name", ""),
                    provider="facebook",
                    provider_id=user_info["id"],
                    user_image=user_info.get("picture", {}).get("data", {}).get("url"),
                    is_active=True,
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.add(user)
                await db.commit()
                await db.refresh(user)
            else:
                # Update existing user
                user.full_name = user_info.get("name", user.full_name)
                user.user_image = user_info.get("picture", {}).get("data", {}).get("url", user.avatar_url)
                user.updated_at = datetime.utcnow()
                await db.commit()

        # Generate secure JWT
        jwt_token = GoogleOAuth.create_secure_jwt(str(user.id))
        
        # Secure response with HTTP-only cookie
        response = JSONResponse(
            content={
                "user_id": UUID(user.user_id),
                "email": user.email,
                "name": user.full_name
            }
        )
        response.set_cookie(
            key="access_token",
            value=jwt_token,
            httponly=True,
            secure=config.settings.ENV == "production",
            samesite="lax",
            max_age=config.settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
        return response

    except IntegrityError:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="User already exists"
        )
        
    except HTTPException as e:
        raise e
        
    except Exception as e:
        # Log full error for debugging
        print(f"Facebook OAuth error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Facebook authentication failed"
        )

# Logout endpoint
@router.post("/logout")
async def logout(token: Annotated[str, Depends(crud.users.oauth2_scheme)], db: AsyncSession = Depends(get_session)):
    # Retrieve the token from the database
    db_token = await crud.token.get_token(db, token)
    if not db_token:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Token not found"
        )
    
    # Update the token status to revoked
    updated_token = await crud.token.update_token_status(db, token, status=False)
    if not updated_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to revoke token"
        )
    
    return {"detail": "Logout successful"}

