"""fixed user workspace warning error

Revision ID: 915cf9b913cb
Revises: ccd0fab49053
Create Date: 2025-05-22 23:52:25.338025

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '915cf9b913cb'
down_revision: Union[str, None] = 'ccd0fab49053'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('workspaces', sa.Column('email', sa.String(length=250), nullable=False))
    op.create_index(op.f('ix_workspaces_email'), 'workspaces', ['email'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_workspaces_email'), table_name='workspaces')
    op.drop_column('workspaces', 'email')
    # ### end Alembic commands ###
