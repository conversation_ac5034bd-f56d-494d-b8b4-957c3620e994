from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import chat

router = APIRouter(prefix="/chat", tags=["chat"])

@router.get("/user/profile", response_model=schemas.UserProfileOut)
async def get_user_profile_route(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await chat.get_user_profile(current_user.user_id, db)

@router.get("/chat/search", response_model=schemas.ChatSessionWithMessages)
async def search_chat(task_name: str = Query(..., description="Task name to search chat by"),
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await chat.search_chat_by_task_name(task_name, db)

@router.get("/chat/tasks", response_model=List[schemas.TaskChatSummary])
async def fetch_all_task_chats(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await chat.get_all_task_chat(db)

@router.get("/chat/team/{team_id}", response_model=schemas.TeamMessageDetail)
async def fetch_team_message_detail(
    team_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await chat.get_team_message_detail(team_id, db)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.get("/chat/session/{chat_session_id}/messages", response_model=schemas.ChatSessionMessages)
async def fetch_chat_session_messages(
    chat_session_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        return await chat.get_chat_session_messages(chat_session_id, db)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@router.post("/chat/send", response_model=None, status_code=201)
async def create_chat_message(
    payload: schemas.SendMessageCreate,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    try:
        await chat.send_message(payload, db)
        return {"detail": "Message sent successfully."}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))