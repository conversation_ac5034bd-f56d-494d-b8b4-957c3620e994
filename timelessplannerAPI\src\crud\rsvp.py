from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def get_all_guest_tags_with_rsvp_true(db: AsyncSession) -> List[schemas.GuestTagRSVPStatus]:
    # Get all GuestTags joined with Event
    result = await db.execute(
        select(
            models.GuestTag.guest_tag_id,
            models.GuestTag.guest_tag_name,
            models.Event.event_name,
            func.count(models.Guest.guest_id).label("guest_count"),
            models.RSVPForm.rsvp_form_id
        )
        .join(models.Event, models.GuestTag.event_id == models.Event.event_id)
        .outerjoin(models.Guest, models.Guest.guest_tag_id == models.GuestTag.guest_tag_id)
        .outerjoin(models.RSVPForm, models.RSVPForm.guest_tag_id == models.GuestTag.guest_tag_id)
        .group_by(models.GuestTag.guest_tag_id, models.GuestTag.guest_tag_name, models.Event.event_name, models.RSVPForm.rsvp_form_id)
    )

    guest_tag_list = []
    for row in result.all():
        guest_tag_list.append(
            schemas.GuestTagRSVPStatus(
                guest_tag_name=row.guest_tag_name,
                event_name=row.event_name,
                total_guests=row.guest_count,
                rsvp_status="configured" if row.rsvp_form_id else "not-configured"
            )
        )

    return guest_tag_list

async def configure_guest_tag_rsvp_form(payload: schemas.RSVPFormCreate, db: AsyncSession) -> models.RSVPForm:
    # Check if RSVPForm already exists for the given guest_tag_id
    result = await db.execute(
        select(models.RSVPForm).where(models.RSVPForm.guest_tag_id == payload.guest_tag_id)
    )
    existing_rsvp = result.scalar_one_or_none()
    if existing_rsvp:
        raise HTTPException(status_code=400, detail="RSVP already configured for this guest tag")

    new_rsvp = models.RSVPForm(
        guest_tag_id=payload.guest_tag_id,
        rsvp_title=payload.rsvp_title,
        rsvp_sub_message=payload.rsvp_sub_message,
        event_logo=payload.event_logo,
        button_type=payload.button_type,
        sender_email=payload.sender_email,
        sender_reply_email=payload.sender_reply_email,
        responsive=payload.responsive
    )
    db.add(new_rsvp)
    await db.commit()
    await db.refresh(new_rsvp)
    return new_rsvp

async def get_all_configured_guest_rsvp_forms(user_id: UUID, db: AsyncSession) -> List[schemas.ConfiguredRSVPFormOut]:
    result = await db.execute(
        select(
            models.GuestTag.name.label("guest_tag_name"),
            models.Event.event_name,
            func.count(models.Guest.guest_id).label("total_guests")
        )
        .join(models.RSVPForm, models.RSVPForm.guest_tag_id == models.GuestTag.guest_tag_id)
        .join(models.Event, models.GuestTag.event_id == Event.event_id)
        .outerjoin(models.Guest, models.Guest.rsvp_form_id == models.RSVPForm.rsvp_form_id)
        .where(models.GuestTag.user_id == user_id)
        .group_by(models.GuestTag.name, models.Event.event_name)
    )
    
    rows = result.all()

    return [
        schemas.ConfiguredRSVPFormOut(
            guest_tag_name=row.guest_tag_name,
            event_name=row.event_name,
            total_guests=row.total_guests,
            rsvp_status="configured"
        ) for row in rows
    ]