from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def add_team(db: AsyncSession, team_data: schemas.TeamCreate) -> schemas.TeamResponse:
    new_team = models.Team(
        team_id=uuid4(),
        team_name=team_data.team_name,
        event_id=team_data.event_id
    )
    db.add(new_team)
    await db.commit()
    await db.refresh(new_team)

    return schemas.TeamResponse(
        team_id=new_team.team_id,
        team_name=new_team.team_name,
        event_id=new_team.event_id
    )

async def add_team_member_to_team(db: AsyncSession, member_data: schemas.TeamMemberCreate) -> schemas.TeamMemberResponse:
    new_member = models.TeamMember(
        team_member_id=uuid4(),
        member_name=member_data.member_name,
        email=member_data.email,
        role=member_data.role,
        description=member_data.description,
        team_id=member_data.team_id,
        user_id=uuid4()  # Replace with actual logic if linking to an existing user
    )

    db.add(new_member)
    await db.commit()
    await db.refresh(new_member)

    return schemas.TeamMemberResponse(
        team_member_id=new_member.team_member_id,
        member_name=new_member.member_name,
        email=new_member.email,
        role=new_member.role,
        description=new_member.description,
        team_id=new_member.team_id
    )

async def get_team_members_overview(db: AsyncSession, team_id: UUID) -> schemas.TeamMembersOverviewResponse:
    result = await db.execute(
        select(models.TeamMember).where(models.TeamMember.team_id == team_id)
    )
    members = result.scalars().all()

    return schemas.TeamMembersOverviewResponse(
        members=[
            schemas.TeamMemberOverview(
                image=member.image,
                member_name=member.member_name,
                skill=member.skill,
                role=member.role,
                date_added=member.date_added,
                email=member.email
            ) for member in members
        ]
    ) 

async def get_team_analysis(db: AsyncSession, user_id: UUID) -> schemas.TeamAnalysisResponse:
    # Total teams created by user
    total_teams = await db.scalar(
        select(func.count()).select_from(models.Team).where(models.Team.created_by == user_id)
    )

    # Team IDs created by user
    team_ids_result = await db.execute(
        select(models.Team.team_id).where(models.Team.created_by == user_id)
    )
    team_ids = [row[0] for row in team_ids_result.all()]

    # Total members in these teams
    total_members = await db.scalar(
        select(func.count()).select_from(models.TeamMember).where(models.TeamMember.team_id.in_(team_ids))
    )

    # Total leads (assuming "role = 'lead'")
    total_leads = await db.scalar(
        select(func.count()).select_from(models.TeamMember).where(
            models.TeamMember.team_id.in_(team_ids), models.TeamMember.role.ilike("%lead%")
        )
    )

    # Total tasks assigned to these teams
    total_tasks = await db.scalar(
        select(func.count(models.Task.task_id))
        .select_from(models.task_teams)
        .where(models.task_teams.c.team_id.in_(team_ids))
    )

    return schemas.TeamAnalysisResponse(
        total_teams=total_teams or 0,
        total_leads=total_leads or 0,
        total_members=total_members or 0,
        total_tasks=total_tasks or 0
    ) 

async def get_single_team_analysis(db: AsyncSession, team_id: UUID) -> schemas.SingleTeamAnalysisResponse:
    # Get the team
    result = await db.execute(select(models.Team).where(models.Team.team_id == team_id))
    team = result.scalar_one_or_none()
    if not team:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Team not found")

    # Count total members
    total_members = await db.scalar(
        select(func.count()).select_from(models.TeamMember).where(models.TeamMember.team_id == team_id)
    )

    # Count leads (assuming role contains "lead")
    total_leads = await db.scalar(
        select(func.count()).select_from(models.TeamMember).where(
            models.TeamMember.team_id == team_id, models.TeamMember.role.ilike("%lead%")
        )
    )

    # Get first five member images (non-null only)
    image_results = await db.execute(
        select(models.TeamMember.image)
        .where(models.TeamMember.team_id == team_id, models.TeamMember.image.isnot(None))
        .limit(5)
    )
    member_images = [row[0] for row in image_results.all()]

    return schemas.SingleTeamAnalysisResponse(
        team_name=team.team_name,
        total_leads=total_leads or 0,
        total_members=total_members or 0,
        member_images=member_images
    )