"""Fix foreign key order

Revision ID: d736909dd4f4
Revises: 
Create Date: 2025-05-22 23:25:21.483176

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd736909dd4f4'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('notifications',
    sa.Column('notification_id', sa.Uuid(), nullable=False),
    sa.Column('triggered_at', sa.DateTime(), nullable=False),
    sa.Column('message_heading', sa.String(length=250), nullable=False),
    sa.Column('message_body', sa.Text(), nullable=False),
    sa.PrimaryKeyConstraint('notification_id')
    )
    op.create_table('teams',
    sa.Column('team_id', sa.Uuid(), nullable=False),
    sa.Column('team_name', sa.String(length=100), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('team_id')
    )
    op.create_table('user',
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('first_name', sa.String(length=50), nullable=True),
    sa.Column('last_name', sa.String(length=50), nullable=True),
    sa.Column('job_title', sa.String(length=100), nullable=True),
    sa.Column('email', sa.String(length=250), nullable=False),
    sa.Column('phone_number', sa.String(length=16), nullable=True),
    sa.Column('password', sa.String(length=128), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('provider', sa.String(length=50), nullable=True),
    sa.Column('provider_id', sa.String(length=100), nullable=True),
    sa.Column('user_image', sa.String(length=250), nullable=True),
    sa.Column('notification_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['notification_id'], ['notifications.notification_id'], ),
    sa.PrimaryKeyConstraint('user_id'),
    sa.UniqueConstraint('phone_number')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_table('billing_plans',
    sa.Column('billing_plan_id', sa.Uuid(), nullable=False),
    sa.Column('plan_name', sa.String(length=100), nullable=False),
    sa.Column('amount_paid', sa.Float(), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('initial_plan', sa.String(length=100), nullable=True),
    sa.Column('payment_method', sa.String(length=50), nullable=True),
    sa.Column('billing_contact', sa.String(length=250), nullable=True),
    sa.Column('billing_email', sa.String(length=250), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('billing_plan_id')
    )
    op.create_table('crm_integrations',
    sa.Column('crm_integration_id', sa.Uuid(), nullable=False),
    sa.Column('provider_name', sa.String(length=100), nullable=False),
    sa.Column('url_link', sa.String(length=250), nullable=False),
    sa.Column('integrated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('crm_integration_id')
    )
    op.create_table('domains',
    sa.Column('domain_id', sa.Uuid(), nullable=False),
    sa.Column('timeless_domain', sa.Boolean(), nullable=False),
    sa.Column('company_name', sa.String(length=250), nullable=False),
    sa.Column('custom_domain', sa.Boolean(), nullable=False),
    sa.Column('custom_domain_name', sa.String(length=250), nullable=True),
    sa.Column('verification_status', sa.String(length=50), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=True),
    sa.Column('name', sa.String(length=250), nullable=True),
    sa.Column('value', sa.String(length=250), nullable=True),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('domain_id')
    )
    op.create_table('notification_preferences',
    sa.Column('preference_id', sa.Uuid(), nullable=False),
    sa.Column('general_notification', sa.Boolean(), nullable=False),
    sa.Column('task_completion', sa.Boolean(), nullable=False),
    sa.Column('event_creation_and_completion', sa.Boolean(), nullable=False),
    sa.Column('system_alert', sa.Boolean(), nullable=False),
    sa.Column('notification_method', sa.String(length=50), nullable=False),
    sa.Column('frequency', sa.String(length=50), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('preference_id')
    )
    op.create_table('password_reset_tokens',
    sa.Column('password_reset_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('used', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('password_reset_id')
    )
    op.create_index(op.f('ix_password_reset_tokens_token'), 'password_reset_tokens', ['token'], unique=True)
    op.create_table('projects',
    sa.Column('project_id', sa.Uuid(), nullable=False),
    sa.Column('project_name', sa.String(length=200), nullable=False),
    sa.Column('project_description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_email', sa.String(length=250), nullable=False),
    sa.ForeignKeyConstraint(['user_email'], ['user.email'], ),
    sa.PrimaryKeyConstraint('project_id')
    )
    op.create_table('team_members',
    sa.Column('member_id', sa.Uuid(), nullable=False),
    sa.Column('member_name', sa.String(length=100), nullable=False),
    sa.Column('email', sa.String(length=250), nullable=False),
    sa.Column('role', sa.String(length=100), nullable=False),
    sa.Column('description', sa.String(length=250), nullable=True),
    sa.Column('image', sa.String(length=250), nullable=True),
    sa.Column('skill', sa.String(length=100), nullable=True),
    sa.Column('date_added', sa.DateTime(), nullable=False),
    sa.Column('team_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['team_id'], ['teams.team_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('member_id')
    )
    op.create_table('token',
    sa.Column('token_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('access_token', sa.String(length=450), nullable=False),
    sa.Column('refresh_token', sa.String(length=450), nullable=False),
    sa.Column('status', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('revoked_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('token_id', 'access_token'),
    sa.UniqueConstraint('access_token'),
    sa.UniqueConstraint('refresh_token')
    )
    op.create_table('user_otp',
    sa.Column('otp_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('email', sa.String(length=250), nullable=False),
    sa.Column('otp_code', sa.String(length=4), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('otp_id')
    )
    op.create_index(op.f('ix_user_otp_email'), 'user_otp', ['email'], unique=False)
    op.create_table('workspaces',
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('workspace_name', sa.String(length=200), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('workspace_id')
    )
    op.create_table('events',
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('event_name', sa.String(length=200), nullable=False),
    sa.Column('project_id', sa.Uuid(), nullable=False),
    sa.Column('team', sa.Boolean(), nullable=False),
    sa.Column('event_type', sa.String(length=50), nullable=True),
    sa.Column('event_location_type', sa.String(length=50), nullable=True),
    sa.Column('event_location', sa.String(length=250), nullable=True),
    sa.Column('street_address', sa.String(length=250), nullable=True),
    sa.Column('state', sa.String(length=100), nullable=True),
    sa.Column('state_initial', sa.String(length=10), nullable=True),
    sa.Column('zip_code', sa.String(length=20), nullable=True),
    sa.Column('country', sa.String(length=100), nullable=True),
    sa.Column('time_zone', sa.String(length=50), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('start_time', sa.Time(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('end_time', sa.Time(), nullable=True),
    sa.Column('logo', sa.String(length=250), nullable=True),
    sa.Column('industry', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('team_name', sa.String(length=100), nullable=True),
    sa.Column('team_lead_email', sa.String(length=250), nullable=True),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['project_id'], ['projects.project_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('event_id')
    )
    op.create_table('ai_forms',
    sa.Column('ai_form_id', sa.Uuid(), nullable=False),
    sa.Column('form_title', sa.String(length=200), nullable=False),
    sa.Column('share_link', sa.String(length=250), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('notification_id', sa.Uuid(), nullable=True),
    sa.ForeignKeyConstraint(['event_id'], ['events.event_id'], ),
    sa.ForeignKeyConstraint(['notification_id'], ['notifications.notification_id'], ),
    sa.PrimaryKeyConstraint('ai_form_id')
    )
    op.create_table('broadcasts',
    sa.Column('broadcast_id', sa.Uuid(), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('message_subject', sa.String(length=200), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('medium', sa.String(length=50), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['event_id'], ['events.event_id'], ),
    sa.PrimaryKeyConstraint('broadcast_id')
    )
    op.create_table('event_team',
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('team_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['event_id'], ['events.event_id'], ),
    sa.ForeignKeyConstraint(['team_id'], ['teams.team_id'], ),
    sa.PrimaryKeyConstraint('event_id', 'team_id')
    )
    op.create_table('tasks',
    sa.Column('task_id', sa.Uuid(), nullable=False),
    sa.Column('task_name', sa.String(length=200), nullable=False),
    sa.Column('task_description', sa.Text(), nullable=True),
    sa.Column('start_date', sa.Date(), nullable=True),
    sa.Column('end_date', sa.Date(), nullable=True),
    sa.Column('task_instruction', sa.Text(), nullable=True),
    sa.Column('attachment_url', sa.String(length=250), nullable=True),
    sa.Column('maximum_team', sa.Integer(), nullable=True),
    sa.Column('priority', sa.String(length=50), nullable=True),
    sa.Column('linked_to_event', sa.Boolean(), nullable=False),
    sa.Column('days_left', sa.Integer(), nullable=True),
    sa.Column('progress_value', sa.Float(), nullable=True),
    sa.Column('task_status', sa.String(length=50), nullable=True),
    sa.Column('total_chats', sa.Integer(), nullable=True),
    sa.Column('team_name', sa.String(length=100), nullable=True),
    sa.Column('team_lead_email', sa.String(length=250), nullable=True),
    sa.Column('team_member_images', sa.Text(), nullable=True),
    sa.Column('progress_description', sa.Text(), nullable=True),
    sa.Column('progress_status', sa.String(length=50), nullable=True),
    sa.Column('completion_percentage', sa.Float(), nullable=True),
    sa.Column('days_since_completion', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['event_id'], ['events.event_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('task_id')
    )
    op.create_table('vendors',
    sa.Column('vendor_id', sa.Uuid(), nullable=False),
    sa.Column('service_name', sa.String(length=200), nullable=False),
    sa.Column('email', sa.String(length=250), nullable=True),
    sa.Column('phone_number', sa.String(length=20), nullable=True),
    sa.Column('office_address', sa.String(length=250), nullable=True),
    sa.Column('star_rating', sa.Float(), nullable=True),
    sa.Column('total_ratings', sa.Integer(), nullable=True),
    sa.Column('last_opened', sa.DateTime(), nullable=True),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['event_id'], ['events.event_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.user_id'], ),
    sa.PrimaryKeyConstraint('vendor_id')
    )
    op.create_table('ai_form_response_logics',
    sa.Column('response_logic_id', sa.Uuid(), nullable=False),
    sa.Column('ai_form_id', sa.Uuid(), nullable=False),
    sa.Column('email_logic', sa.Boolean(), nullable=False),
    sa.Column('email_subject', sa.String(length=200), nullable=True),
    sa.Column('sender_name', sa.String(length=100), nullable=True),
    sa.Column('sender_email', sa.String(length=250), nullable=True),
    sa.Column('reply_email', sa.String(length=250), nullable=True),
    sa.ForeignKeyConstraint(['ai_form_id'], ['ai_forms.ai_form_id'], ),
    sa.PrimaryKeyConstraint('response_logic_id')
    )
    op.create_table('ai_form_themes',
    sa.Column('theme_id', sa.Uuid(), nullable=False),
    sa.Column('ai_form_id', sa.Uuid(), nullable=False),
    sa.Column('theme_color', sa.String(length=50), nullable=True),
    sa.Column('brand_logo', sa.String(length=250), nullable=True),
    sa.Column('background_image', sa.String(length=250), nullable=True),
    sa.Column('layout_color', sa.String(length=50), nullable=True),
    sa.Column('text_color', sa.String(length=50), nullable=True),
    sa.Column('button_color', sa.String(length=50), nullable=True),
    sa.Column('button_text_color', sa.String(length=50), nullable=True),
    sa.Column('background_color', sa.String(length=50), nullable=True),
    sa.Column('type_face', sa.String(length=50), nullable=True),
    sa.Column('text_animation', sa.String(length=50), nullable=True),
    sa.Column('button_radius', sa.Float(), nullable=True),
    sa.Column('background_opacity', sa.Float(), nullable=True),
    sa.Column('logo', sa.String(length=250), nullable=True),
    sa.ForeignKeyConstraint(['ai_form_id'], ['ai_forms.ai_form_id'], ),
    sa.PrimaryKeyConstraint('theme_id')
    )
    op.create_table('chat_sessions',
    sa.Column('chat_session_id', sa.Uuid(), nullable=False),
    sa.Column('session_name', sa.String(length=100), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('task_id', sa.Uuid(), nullable=False),
    sa.Column('team_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.task_id'], ),
    sa.ForeignKeyConstraint(['team_id'], ['teams.team_id'], ),
    sa.PrimaryKeyConstraint('chat_session_id'),
    sa.UniqueConstraint('task_id', 'team_id', name='_task_team_uc')
    )
    op.create_table('feedback_questions',
    sa.Column('feedback_question_id', sa.Uuid(), nullable=False),
    sa.Column('ai_form_id', sa.Uuid(), nullable=False),
    sa.Column('question_initial', sa.String(length=100), nullable=False),
    sa.Column('question', sa.Text(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['ai_form_id'], ['ai_forms.ai_form_id'], ),
    sa.PrimaryKeyConstraint('feedback_question_id')
    )
    op.create_table('guest_tags',
    sa.Column('guest_tag_id', sa.Uuid(), nullable=False),
    sa.Column('tag_name', sa.String(length=100), nullable=False),
    sa.Column('event_id', sa.Uuid(), nullable=False),
    sa.Column('rsvp_form', sa.Boolean(), nullable=False),
    sa.Column('name_badge_printing', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('broadcast_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['broadcast_id'], ['broadcasts.broadcast_id'], ),
    sa.ForeignKeyConstraint(['event_id'], ['events.event_id'], ),
    sa.PrimaryKeyConstraint('guest_tag_id')
    )
    op.create_table('task_teams',
    sa.Column('task_id', sa.Uuid(), nullable=False),
    sa.Column('team_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.task_id'], ),
    sa.ForeignKeyConstraint(['team_id'], ['teams.team_id'], ),
    sa.PrimaryKeyConstraint('task_id', 'team_id')
    )
    op.create_table('chat_messages',
    sa.Column('message_id', sa.Uuid(), nullable=False),
    sa.Column('chat_session_id', sa.Uuid(), nullable=False),
    sa.Column('message', sa.Text(), nullable=True),
    sa.Column('attachment', sa.String(length=250), nullable=True),
    sa.Column('media', sa.String(length=250), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('sender_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['chat_session_id'], ['chat_sessions.chat_session_id'], ),
    sa.ForeignKeyConstraint(['sender_id'], ['team_members.member_id'], ),
    sa.PrimaryKeyConstraint('message_id')
    )
    op.create_table('rsvp_forms',
    sa.Column('rsvp_form_id', sa.Uuid(), nullable=False),
    sa.Column('guest_tag_id', sa.Uuid(), nullable=False),
    sa.Column('rsvp_title', sa.String(length=200), nullable=False),
    sa.Column('rsvp_sub_message', sa.String(length=250), nullable=True),
    sa.Column('event_logo', sa.String(length=250), nullable=True),
    sa.Column('button_type', sa.String(length=50), nullable=False),
    sa.Column('sender_email', sa.String(length=250), nullable=False),
    sa.Column('sender_reply_email', sa.String(length=250), nullable=False),
    sa.Column('responsive', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['guest_tag_id'], ['guest_tags.guest_tag_id'], ),
    sa.PrimaryKeyConstraint('rsvp_form_id')
    )
    op.create_table('guests',
    sa.Column('guest_id', sa.Uuid(), nullable=False),
    sa.Column('salutation', sa.String(length=10), nullable=True),
    sa.Column('first_name', sa.String(length=100), nullable=False),
    sa.Column('last_name', sa.String(length=100), nullable=False),
    sa.Column('email', sa.String(length=250), nullable=False),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('seating_style', sa.String(length=50), nullable=True),
    sa.Column('color', sa.String(length=50), nullable=True),
    sa.Column('external_id', sa.String(length=100), nullable=True),
    sa.Column('affiliation', sa.String(length=100), nullable=True),
    sa.Column('secondary_email', sa.String(length=250), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('rsvp', sa.Boolean(), nullable=False),
    sa.Column('checked_in', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('guest_tag_id', sa.Uuid(), nullable=True),
    sa.Column('rsvp_form_id', sa.Uuid(), nullable=False),
    sa.Column('ai_form_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['ai_form_id'], ['ai_forms.ai_form_id'], ),
    sa.ForeignKeyConstraint(['guest_tag_id'], ['guest_tags.guest_tag_id'], ),
    sa.ForeignKeyConstraint(['rsvp_form_id'], ['rsvp_forms.rsvp_form_id'], ),
    sa.PrimaryKeyConstraint('guest_id')
    )
    op.create_table('notification_settings',
    sa.Column('notification_settings_id', sa.Uuid(), nullable=False),
    sa.Column('email_recipients', sa.String(length=250), nullable=True),
    sa.Column('text_recipients', sa.String(length=250), nullable=True),
    sa.Column('guest_id', sa.Uuid(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['guest_id'], ['guests.guest_id'], ),
    sa.PrimaryKeyConstraint('notification_settings_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('notification_settings')
    op.drop_table('guests')
    op.drop_table('rsvp_forms')
    op.drop_table('chat_messages')
    op.drop_table('task_teams')
    op.drop_table('guest_tags')
    op.drop_table('feedback_questions')
    op.drop_table('chat_sessions')
    op.drop_table('ai_form_themes')
    op.drop_table('ai_form_response_logics')
    op.drop_table('vendors')
    op.drop_table('tasks')
    op.drop_table('event_team')
    op.drop_table('broadcasts')
    op.drop_table('ai_forms')
    op.drop_table('events')
    op.drop_table('workspaces')
    op.drop_index(op.f('ix_user_otp_email'), table_name='user_otp')
    op.drop_table('user_otp')
    op.drop_table('token')
    op.drop_table('team_members')
    op.drop_table('projects')
    op.drop_index(op.f('ix_password_reset_tokens_token'), table_name='password_reset_tokens')
    op.drop_table('password_reset_tokens')
    op.drop_table('notification_preferences')
    op.drop_table('domains')
    op.drop_table('crm_integrations')
    op.drop_table('billing_plans')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    op.drop_table('teams')
    op.drop_table('notifications')
    # ### end Alembic commands ###
