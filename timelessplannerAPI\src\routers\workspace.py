from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import workspace


router = APIRouter(prefix="/workspace", tags=["workspace"])

@router.post("/workspaces", response_model=schemas.WorkspaceResponse, status_code=201)
async def create_workspace_route(
    data: schemas.WorkspaceCreateWithEmail,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await workspace.create_workspace(data=data, db=db)