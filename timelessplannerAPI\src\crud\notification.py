from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas

async def get_triggered_notifications_for_user(user_id: UUID, db: AsyncSession) -> List[models.Notification]:
    result = await db.execute(
        select(models.Notification)
        .join(models.Notification.users)
        .where(models.User.user_id == user_id)
        .order_by(models.Notification.triggered_at.desc())
    )
    return result.scalars().all()

async def set_notification_preferences(
    user_id: UUID,
    pref_data: schemas.NotificationPreferenceCreate,
    db: AsyncSession
) -> str:
    # Check if preference already exists for user
    existing_pref = await db.execute(
        select(models.NotificationPreference).where(models.NotificationPreference.user_id == user_id)
    )
    existing = existing_pref.scalar_one_or_none()

    if existing:
        # Update existing
        existing.general_notification = pref_data.general_notification
        existing.task_completion = pref_data.task_completion
        existing.event_creation_and_completion = pref_data.event_creation_and_completion
        existing.system_alert = pref_data.system_alert
        existing.notification_method = pref_data.notification_method
        existing.frequency = pref_data.frequency
    else:
        # Create new preference
        new_pref = models.NotificationPreference(
            user_id=user_id,
            general_notification=pref_data.general_notification,
            task_completion=pref_data.task_completion,
            event_creation_and_completion=pref_data.event_creation_and_completion,
            system_alert=pref_data.system_alert,
            notification_method=pref_data.notification_method,
            frequency=pref_data.frequency
        )
        db.add(new_pref)

    await db.commit()
    return "200_OK"