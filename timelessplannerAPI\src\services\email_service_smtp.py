from src.utils.commonImports import *
import ssl
import smtplib
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MIMEText
import logging
import time
from src.utils.config import settings
from typing import Annotated
from pydantic import EmailStr

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MailerInterface:
    """
    Interface to define standard behavior for any mailer service.
    """
    async def send(
        self, subject: str, recipients: List[str],
        body: Optional[str] = None, html_body: Optional[str] = None
    ):
        raise NotImplementedError


class SMTPMailer(MailerInterface):
    """
    Default SMTP mailer implementation.
    """

    async def send(
        self, subject: str, recipients: List[str],
        body: Optional[str] = None, html_body: Optional[str] = None,
        max_retries: int = 3
    ):
        msg = MIMEMultipart("alternative")
        msg['From'] = settings.MAIL_FROM
        msg['To'] = ", ".join(recipients)
        msg['Subject'] = subject

        if body:
            msg.attach(MIMEText(body, "plain"))
        if html_body:
            msg.attach(MIMEText(html_body, "html"))

        for attempt in range(max_retries):
            try:
                with smtplib.SMTP(settings.MAIL_SERVER, settings.MAIL_PORT) as server:
                    server.ehlo()
                    server.starttls()
                    server.login(settings.MAIL_USERNAME, settings.MAIL_PASSWORD)
                    server.sendmail(settings.MAIL_FROM, recipients, msg.as_string())
                    logger.info(f"Email sent successfully to {recipients}")
                return
            except smtplib.SMTPException as e:
                logger.warning(f"[Attempt {attempt + 1}] Email send failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                else:
                    logger.error(f"Failed to send email after {max_retries} attempts.")
                    raise


class EmailService:
    """
    High-level service for sending application emails.
    """

    mailer: MailerInterface = SMTPMailer()  # Can later switch to SES, Mailgun, etc.

    @staticmethod
    async def send_email(
        subject: str,
        recipients: Union[str, List[str]],
        body: Optional[str] = None,
        html_body: Optional[str] = None,
        max_retries: int = 3
    ):
        """
        Entry point to send an email.
        """
        if not body and not html_body:
            raise ValueError("Either 'body' or 'html_body' must be provided.")

        if isinstance(recipients, str):
            recipients_list = [email.strip() for email in recipients.split(",") if email.strip()]
        elif isinstance(recipients, list):
            if not recipients:
                raise ValueError("Recipient list is empty.")
            recipients_list = recipients
        else:
            raise TypeError("Recipients must be a string or a list of strings.")

        return await EmailService.mailer.send(
            subject=subject,
            recipients=recipients_list,
            body=body,
            html_body=html_body,
            max_retries=max_retries
        )

    @staticmethod
    async def send_invite_email(recipients: Union[str, List[str]], invite_link: str):
        subject = "You're Invited!"
        body = f"Please use the following link to join: {invite_link}"
        return await EmailService.send_email(subject, recipients, body)

    @staticmethod
    async def send_reminder_email(subject: str, recipients: Union[str, List[str]], reminder_message: str):
        return await EmailService.send_email(subject, recipients, body=reminder_message)

    @staticmethod
    async def send_otp_email(recipients: Union[str, List[str]], otp_code: str):
        subject = "Your OTP Code"
        body = f"Your OTP code is {otp_code}. Please use this to complete your verification on timelessplanner.com."
        return await EmailService.send_email(subject, recipients, body)


    @staticmethod
    async def send_password_reset_email(
        recipients: Annotated[str, EmailStr],
        reset_link: str
        ):
        if not reset_link:
            raise ValueError("Reset link must be provided")
        if not recipients:
            raise ValueError("Recipient email must be provided")
        subject = "Reset your timelessplanner.com password"
        body = (
            "You (or someone else) requested a password reset.\n\n"
            f"Click here to reset your password:\n{reset_link}\n\n"
            "If you didn’t request this, you can safely ignore this email."
            )
        return await EmailService.send_email(subject=subject, recipients=recipients, body=body)
