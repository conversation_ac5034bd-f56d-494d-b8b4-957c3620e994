from datetime import datetime, timezone
from dateutil import parser as date_parser

async def make_timezone_aware(dt):
    """
    Convert a datetime string or datetime object to a timezone-aware datetime object.
    
    Args:
        dt (str | datetime): The datetime to convert.
    
    Returns:
        datetime: A timezone-aware datetime object.
    
    Raises:
        ValueError: If the input is neither a string nor a datetime object.
    """
    if isinstance(dt, str):
        try:
            parsed_dt = date_parser.parse(dt)
            return parsed_dt.replace(tzinfo=timezone.utc) if parsed_dt.tzinfo is None else parsed_dt
        except ValueError as e:
            raise ValueError(f"Invalid datetime format: {dt}") from e
    elif isinstance(dt, datetime):
        return dt if dt.tzinfo else dt.replace(tzinfo=timezone.utc)
    else:
        raise ValueError(f"Unsupported type for datetime: {type(dt)}. Must be string or datetime.")
