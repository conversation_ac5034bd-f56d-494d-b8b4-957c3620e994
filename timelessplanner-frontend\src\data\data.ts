import { ProjectData } from "@/types/types";
import { Vendor } from "@/types/types";
import { Task } from "@/types/types";

export const projectData: ProjectData = {
  projects: [
    {
      id: "28cab013-9644-4257-a0a3-b908385e99be",
      title: "Smart Waste Bin",
      description: "IoT-based waste management system",
      createdTime: "March 1, 2025",
      projectDate: "March 10, 2025 - March 20, 2025",
      createdBy: "me",
      events: [
        {
          id: "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
          name: "System Deployment",
          createdAt: "March 1, 2025",
          eventDate: "March 12, 2025",
          eventTime: "10:00 AM",
          eventType: "Physical",
          location: "Central City Park",
          industry: "Environmental Tech",
          teamMembers: [
            { id: "team-001", name: "<PERSON>" },
            { id: "team-002", name: "<PERSON>" },
            { id: "team-003", name: "<PERSON>" },
            { id: "team-004", name: "<PERSON>" },
          ],
          teamLead: [
            { id: "lead-001", name: "<PERSON>", email: "<EMAIL>" },
          ],
          guestTag: [
            {
              id: "tag-001",
              name: "VIP Guests",
              guests: [
                {
                  id: "guest-001",
                  salutation: "Mr.",
                  name: "John Doe",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Round Table",
                  color: "#FF5733",
                  externalLink: "6556",
                  RSVP: true,
                  checkedIn: true,
                  imageUrl: "http://example.com/image.jpg",
                  affiliation: "Brides groom",
                  secondaryEmail: "<EMAIL>",
                  note: "VIP Seat required",
                },
                {
                  id: "guest-012",
                  salutation: "Mrs.",
                  name: "Sarah Brown",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Single",
                  color: "#00C853",
                  externalLink: "6559",
                  RSVP: true,
                  checkedIn: true,
                  imageUrl: "http://example.com/image4.jpg",
                  affiliation: "Guest Speaker",
                  secondaryEmail: "<EMAIL>",
                  note: "Please bring your own snacks",
                },
                {
                  id: "guest-005",
                  salutation: "Mr.",
                  name: "Michael Davis",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Single",
                  color: "#D32F2F",
                  externalLink: "6560",
                  RSVP: true,
                  checkedIn: false,
                  imageUrl: "http://example.com/image5.jpg",
                  affiliation: "Guest Speaker",
                  secondaryEmail: "<EMAIL>",
                  note: "Please bring your own snacks",
                },
              ],
            },
            {
              id: "tag-002",
              name: "Guests",
              guests: [
                {
                  id: "guest-002",
                  salutation: "Ms.",
                  name: "Jane Smith",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Single",
                  color: "#007BFF",
                  externalLink: "6557",
                  RSVP: false,
                  checkedIn: false,
                  imageUrl: "http://example.com/image2.jpg",
                  affiliation: "Bridesmaid",
                  secondaryEmail: "<EMAIL>",
                  note: "Please bring your own snacks",
                },
              ],
            },
            {
              id: "tag-003",
              name: "Guests",
              guests: [
                {
                  id: "guest-003",
                  salutation: "Dr.",
                  name: "Mike Johnson",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Round Table",
                  color: "#FFC107",
                  externalLink: "6558",
                  RSVP: true,
                  checkedIn: false,
                  imageUrl: "http://example.com/image3.jpg",
                  affiliation: "Guest Speaker",
                  secondaryEmail: "<EMAIL>",
                  note: "Please bring your own snacks",
                },
              ],
            },
          ],
        },
        {
          id: "b2c3d4e5-f6a1-7890-abcd-ef2234567891",
          name: "Testing & Calibration",
          createdAt: "March 5, 2025",
          eventDate: "March 15, 2025",
          eventTime: "2:00 PM",
          eventType: "Virtual",
          location: "Tech Lab",
          industry: "Environmental Tech",
          teamMembers: [
            { id: "team-005", name: "Emily" },
            { id: "team-006", name: "Frank" },
            { id: "team-007", name: "Grace" },
          ],
          teamLead: [
            { id: "lead-002", name: "Emily", email: "<EMAIL>" },
          ],
          guestTag: [
            {
              id: "tag-009",
              name: "Developers",
              guests: [],
            },
            {
              id: "tag-008",
              name: "QA & Testers",
              guests: [
                {
                  id: "guest-013",
                  salutation: "Dr.",
                  name: "John Doe",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Round Table",
                  color: "#FF5733",
                  externalLink: "6559",
                  RSVP: true,
                  checkedIn: true,
                  imageUrl: "http://example.com/image4.jpg",
                  affiliation: "Guest Speaker",
                  secondaryEmail: "<EMAIL>",
                  note: "Please bring your own snacks",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: "5fd9fa4b-31f5-4e6f-8a34-2bcf9c6d7e8a",
      title: "Recycling Awareness App",
      description: "An app to educate users on recycling techniques.",
      createdTime: "March 5, 2025",
      projectDate: "March 15, 2025 - March 25, 2025",
      createdBy: "shared",
      events: [
        {
          id: "c3d4e5f6-a1b2-7890-abcd-ef3234567892",
          name: "App Launch",
          createdAt: "March 10, 2025",
          eventDate: "March 16, 2025",
          eventTime: "11:00 AM",
          eventType: "Physical",
          location: "City Hall",
          industry: "Tech",
          teamMembers: [
            { id: "team-008", name: "Hannah" },
            { id: "team-009", name: "Isaac" },
            { id: "team-010", name: "Jack" },
          ],
          teamLead: [
            { id: "lead-003", name: "Hannah", email: "<EMAIL>" },
          ],
          guestTag: [
            {
              id: "tag-006",
              name: "Developers",
              guests: [
                {
                  id: "guest-004",
                  salutation: "Miss.",
                  name: "Hannah Mills",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Round Table",
                  color: "#FF5733",
                  externalLink: "http://example.com",
                  RSVP: true,
                  checkedIn: false,
                  imageUrl: "http://example.com/image.jpg",
                  affiliation: "Tech Corp",
                  secondaryEmail: "<EMAIL>",
                  note: "VIP Seat required",
                },
              ],
            },
          ],
        },
        {
          id: "d4e5f6a1-b2c3-7890-abcd-ef4234567893",
          name: "Community Engagement Session",
          createdAt: "March 12, 2025",
          eventDate: "March 20, 2025",
          eventTime: "4:00 PM",
          eventType: "Virtual",
          location: "Green Park",
          industry: "Environmental Awareness",
          teamMembers: [
            { id: "team-011", name: "Liam" },
            { id: "team-012", name: "Mia" },
            { id: "team-013", name: "Noah" },
            { id: "team-014", name: "Olivia" },
          ],
          teamLead: [
            { id: "lead-003", name: "Hannah", email: "<EMAIL>" },
          ],
          guestTag: [
            {
              id: "tag-006",
              name: "Designers",
              guests: [
                {
                  id: "guest-005",
                  salutation: "Mr.",
                  name: "Liam west",
                  email: "<EMAIL>",
                  phoneNo: "************",
                  seatingStyle: "Round Table",
                  color: "#FF5733",
                  externalLink: "http://example.com",
                  RSVP: true,
                  checkedIn: false,
                  imageUrl: "http://example.com/image.jpg",
                  affiliation: "Tech Corp",
                  secondaryEmail: "<EMAIL>",
                  note: "VIP Seat required",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: "7e8a5fd9-4b31-f52b-6f8a-34c6d7e8a9b0",
      title: "Green Spaces App",
      description:
        "An app to help users find and discover green spaces near them.",
      createdTime: "March 25, 2025",
      projectDate: "March 30, 2025 - April 10, 2025",
      createdBy: "me",
      events: [],
    },
  ],
};

export const vendorData: Vendor[] = [
  {
    id: 1,
    serviceName: "Foodie's Delight Restaurant",
    email: "<EMAIL>",
    phone: "0802 987 3456",
    officeAddress: "25 Adekunle St, Surulere 105028, Lagos",
    rating: 4.5,
    reviews: 120,
  },
  {
    id: 2,
    serviceName: "Event Perfect Caterers",
    email: "<EMAIL>",
    phone: "0803 123 9876",
    officeAddress: "18 Allen Avenue, Ikeja 101233, Lagos",
    rating: 4.7,
    reviews: 90,
  },
  {
    id: 3,
    serviceName: "Green Spaces Nearby",
    email: "<EMAIL>",
    phone: "0804 567 8901",
    officeAddress: "30 Adeola St, Ikoyi 100023, Lagos",
    rating: 4.9,
    reviews: 150,
  },
];

export const tasks: Task[] = [
  {
    id: "task-001",
    taskName: "Install sensors",
    taskDescription:
      "Set up and install all IoT sensors in the smart waste bins.",
    startDate: "March 2, 2025",
    endDate: "March 30, 2025",
    status: "Completed",
    progress: 100,
    taskInstruction: "Ensure all devices are calibrated before deployment.",
    uploadAttachment: "https://example.com/instructions.pdf",
    maximumTeam: 5,
    priorityLevel: "High",
    createdAt: "March 1, 2025",
    Event: true,
    event: {
      id: "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      name: "System Deployment",
    },
    team: [
      { id: "team-001", email: "<EMAIL>", role: "Team Lead" },
      { id: "team-002", email: "<EMAIL>", role: "Team Member" },
      { id: "team-003", email: "<EMAIL>", role: "Team Member" },
    ],
  },
  {
    id: "task-002",
    taskName: "Write user manual",
    taskDescription:
      "Prepare a comprehensive guide for using the smart waste management system.",
    startDate: "March 8, 2025",
    endDate: "April 12, 2025",
    status: "In Progress",
    progress: 75,
    taskInstruction: "Include diagrams and troubleshooting section.",
    uploadAttachment: "https://example.com/user-manual-template.pdf",
    maximumTeam: 3,
    priorityLevel: "Medium",
    createdAt: "March 2, 2025",
    Event: false,
    team: [
      { id: "team-004", email: "<EMAIL>", role: "Team Lead" },
      { id: "team-005", email: "<EMAIL>", role: "Team Member" },
    ],
  },
  {
    id: "task-003",
    taskName: "Conduct training session",
    taskDescription:
      "Train municipal workers on using the monitoring dashboard.",
    startDate: "March 15, 2025",
    endDate: "May 16, 2025",
    status: "Pending",
    progress: 10,
    taskInstruction: "Prepare presentations and conduct a demo session.",
    uploadAttachment: "https://example.com/training-presentation.pptx",
    maximumTeam: 4,
    priorityLevel: "High",
    createdAt: "March 12, 2025",
    Event: true,
    event: {
      id: "c3d4e5f6-a1b2-7890-abcd-ef3234567892",
      name: "App Launch",
    },
    team: [
      { id: "team-006", email: "<EMAIL>", role: "Team Lead" },
      { id: "team-007", email: "<EMAIL>", role: "Team Member" },
    ],
  },
  {
    id: "task-004",
    taskName: "Procure backup batteries",
    taskDescription: "Purchase and stock backup power supplies for all bins.",
    startDate: "March 10, 2025",
    endDate: "June 18, 2025",
    status: "In Progress",
    progress: 62,
    taskInstruction: "Verify vendor certification before finalizing order.",
    uploadAttachment: "https://example.com/vendor-list.pdf",
    maximumTeam: 2,
    priorityLevel: "Low",
    createdAt: "March 15, 2025",
    Event: false,
    team: [{ id: "team-008", email: "<EMAIL>", role: "Team Lead" }],
  },
  {
    id: "task-005",
    taskName: "Post-deployment system check",
    taskDescription: "Verify all sensors are reporting data after deployment.",
    startDate: "March 20, 2025",
    endDate: "May 22, 2025",
    status: "Completed",
    progress: 100,
    taskInstruction: "Run diagnostics and share a report with the team.",
    uploadAttachment: "https://example.com/system-checklist.pdf",
    maximumTeam: 3,
    priorityLevel: "High",
    createdAt: "March 12, 2025",
    Event: true,
    event: {
      id: "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
      name: "System Deployment",
    },
    team: [
      { id: "team-009", email: "<EMAIL>", role: "Team Lead" },
      { id: "team-002", email: "<EMAIL>", role: "Team Member" },
    ],
  },
  {
    id: "task-006",
    taskName: "Update project documentation",
    taskDescription:
      "Compile all notes, changes, and team inputs into the project wiki.",
    startDate: "March 25, 2025",
    endDate: "July 28, 2025",
    status: "Pending",
    progress: 30,
    taskInstruction: "Ensure document versioning is in place.",
    uploadAttachment: "https://example.com/documentation-guide.pdf",
    maximumTeam: 2,
    priorityLevel: "Medium",
    createdAt: "March 5, 2025",
    Event: false,
    team: [{ id: "team-010", email: "<EMAIL>", role: "Team Lead" }],
  },
];
