from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import task

router = APIRouter(prefix="/task", tags=["task"])

@router.post("/tasks", response_model=schemas.CreateTaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task_route(
    task_request: schemas.CreateTaskRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    try:
        return await task.create_task(
            db=db,
            task_data=task_request,
            user_id=current_user["user_id"],
            event_id=task_request.event_id
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")

@router.put("/tasks/{task_id}/details", response_model=schemas.AddTaskDetailResponse)
async def update_task_details(
    task_id: UUID,
    details: schemas.AddTaskDetailRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    try:
        return await task.add_task_detail(db=db, task_id=task_id, detail_data=details)
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update task details: {str(e)}")

@router.post("/tasks/{task_id}/attach-team-members", response_model=schemas.AttachTeamMembersResponse)
async def attach_team_members(
    task_id: UUID,
    request_data: schemas.AttachTeamMembersRequest,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await task.attach_team_members_to_task(db=db, task_id=task_id, request_data=request_data)

@router.get("/tasks/{task_id}/overview", response_model=schemas.TaskOverviewResponse)
async def task_overview(
    task_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await task.get_task_overview(db=db, task_id=task_id)

@router.get("/tasks/{task_id}/detail", response_model=schemas.TaskDetailResponse)
async def task_detail(
    task_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await task.get_task_detail(db=db, task_id=task_id)

@router.get("/tasks/{task_id}/progress", response_model=schemas.TaskProgressResponse)
async def task_progress(
    task_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await task.get_task_progress(db=db, task_id=task_id)

@router.get("/tasks/{task_id}/team-chat", response_model=schemas.TaskTeamChatResponse)
async def task_team_chat(
    task_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await task.get_task_team_chat(db, task_id)

@router.get("/tasks/{task_id}/complete", response_model=schemas.CompleteTaskResponse)
async def complete_task(
    task_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await task.get_complete_task(db, task_id)

@router.get("/tasks/{task_id}/team-members", response_model=List[schemas.TaskTeamMemberResponse])
async def list_task_team_members(
    task_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await task.get_list_task_team(db, task_id)