from typing import Optional
import logging
from fastapi import FastAPI
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from src.routers import (user_auth,dashboard,project)


logger = logging.getLogger(__name__)
app = FastAPI()

origins = [
    "https://localhost.timelessplanner.com",
    "https://www.timelessplanner.com",
    "https://timelessplanner.com",
    "http://localhost:5173",
    "localhost",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

@app.get("/")
async def root():
    return {"message": "Timeless planner"}

# Serve static files, like your service worker and JS
app.mount("/static", StaticFiles(directory="static"), name="static")

# Include router endpoints
app.include_router(user_auth.router)
app.include_router(dashboard.router)
app.include_router(project.router)