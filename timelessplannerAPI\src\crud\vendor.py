from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas
from src.utils import config
from src.services.email_service import EmailService
from src.services.sms_service import send_notification_sms

def search_vendor(service_name: str, location: Optional[str] = None):
    # Construct the query
    query = service_name
    if location:
        query += f" in {location}"
    
    # Step 1: Search for the place
    search_params = {
        "query": query,
        "key": config.settings.GOOGLE_API_KEY
    }
    search_response = requests.get(PLACES_SEARCH_URL, params=search_params)
    search_results = search_response.json()

    if not search_results.get("results"):
        return {"error": "No results found."}

    # Take the first result
    place = search_results["results"][0]
    place_id = place["place_id"]

    # Step 2: Get place details
    details_params = {
        "place_id": place_id,
        "fields": "name,rating,user_ratings_total,formatted_address,formatted_phone_number",
        "key": config.settings.GOOGLE_API_KEY
    }
    details_response = requests.get(PLACE_DETAILS_URL, params=details_params)
    details = details_response.json().get("result", {})

    # Construct the vendor information
    vendor_info = {
        "vendor_name": details.get("name"),
        "star_rating": details.get("rating"),
        "star_total_value": details.get("user_ratings_total"),
        "vendor_address": details.get("formatted_address"),
        "phone_number": details.get("formatted_phone_number"),
        "last_opened": datetime.utcnow().isoformat()
    }

    return vendor_info

async def add_vendor_manually(vendor_data: schemas.VendorCreate, user_id, event_id, db: AsyncSession):
    new_vendor = models.Vendor(
        vendor_id=uuid4(),
        service_name=vendor_data.service_name,
        email=vendor_data.email,
        phone_number=vendor_data.phone_number,
        office_address=vendor_data.office_address,
        star_rating=vendor_data.star_rating,
        total_ratings=1 if vendor_data.star_rating else 0,
        last_opened=datetime.utcnow(),
        user_id=user_id,
        event_id=event_id
    )
    db.add(new_vendor)
    await db.commit()
    await db.refresh(new_vendor)
    return new_vendor

async def view_vendors(db: AsyncSession) -> List[models.Vendor]:
    result = await db.execute(select(models.Vendor))
    return result.scalars().all()

async def email_vendor(vendor_email: str, message: str):
    subject = "Message from Event Organizer"
    await EmailService.send_email(subject=subject, recipients=vendor_email, body=message)
    return {"message": "Email sent successfully"}

async def text_vendor(phone_number: str, message: str):
    """
    Send an SMS to a vendor.
    """
    return await send_notification_sms(contacts=phone_number, message=message)