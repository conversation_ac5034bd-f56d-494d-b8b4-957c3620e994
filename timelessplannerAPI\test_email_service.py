import asyncio
from src.services.email_service import EmailService

async def test_send_emails():
    test_recipient = "patrick<PERSON><PERSON><PERSON>@gmail.com"
    test_invite_link = "https://timelessplanner.com/invite?code=123456"
    test_reset_link = "https://timelessplanner.com/reset-password?token=abcdef"
    test_otp_code = "987654"
    test_reminder_message = "Don't forget to submit your weekly plan!"

    print("\n--- Testing Invite Email ---")
    await EmailService.send_invite_email(test_recipient, test_invite_link)

    print("\n--- Testing Reminder Email ---")
    await EmailService.send_reminder_email("Weekly Reminder", test_recipient, test_reminder_message)

    print("\n--- Testing OTP Email ---")
    await EmailService.send_otp_email(test_recipient, test_otp_code)

    print("\n--- Testing Password Reset Email ---")
    await EmailService.send_password_reset_email(test_recipient, test_reset_link)

if __name__ == "__main__":
    asyncio.run(test_send_emails())
