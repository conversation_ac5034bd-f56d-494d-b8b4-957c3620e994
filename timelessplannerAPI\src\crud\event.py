import pandas as pd
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile
from fastapi.responses import StreamingResponse
from src.utils.commonImports import *
from src.models import models
from src.schemas import schemas
from io import BytesIO


async def add_event(event_data: schemas.EventCreate, db: AsyncSession, user_email: str):
    new_event = models.Event(
        event_id=uuid4(),
        event_name=event_data.event_name,
        project_id=event_data.project_id,
        team=event_data.team,
        user_email=user_email,
        created_at=datetime.utcnow()
    )
    db.add(new_event)
    await db.commit()
    await db.refresh(new_event)
    return new_event


async def add_event_detail(event_id: UUID, event_data: schemas.EventDetailUpdate, db: AsyncSession):
    result = await db.execute(select(models.Event).where(models.Event.event_id == event_id))
    event = result.scalar_one_or_none()

    if not event:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Event not found.")

    for field, value in event_data.dict(exclude_unset=True).items():
        setattr(event, field, value)

    await db.commit()
    await db.refresh(event)
    return event

async def get_all_event_overview(user_id: UUID, db: AsyncSession):
    # Fetch events where the user is the creator
    result = await db.execute(
        select(models.Event)
        .options(joinedload(models.Event.project), joinedload(models.Event.guest_tags))
        .where(models.Event.user_id == user_id)
    )
    created_events = result.scalars().all()

    # Fetch events where the user is a team member
    result = await db.execute(
        select(models.Event)
        .join(models.Event.teams)
        .join(models.Team.members)
        .options(joinedload(models.Event.project), joinedload(models.Event.guest_tags))
        .where(models.TeamMember.user_id == user_id)
    )
    team_events = result.scalars().all()

    # Merge both lists (remove duplicates using event_id)
    all_events = {event.event_id: event for event in created_events + team_events}.values()

    # Create a summarized response
    event_overviews = []
    for event in all_events:
        total_guests = sum(len(tag.guests) if tag.guests else 0 for tag in event.guest_tags)
        guest_tag_count = len(event.guest_tags)

        event_overviews.append({
            "event_name": event.event_name,
            "project_name": event.project.project_name if event.project else None,
            "total_guest": total_guests,
            "guest_tag_number": guest_tag_count,
        })

    return event_overviews

async def get_overview_of_event(event_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(models.Event)
        .options(
            joinedload(models.Event.teams).joinedload(models.Team.members),
            joinedload(models.Event.project)
        )
        .where(models.Event.event_id == event_id)
    )
    event = result.scalars().first()

    if not event:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Event not found")

    # Flatten and structure the response
    team_members = []
    team_name = None
    team_lead_email = None

    if event.teams:
        team = event.teams[0]  # assuming one team per event
        team_name = team.team_name
        team_lead_email = event.team_lead_email  # from event table
        team_members = [member.image for member in team.members[:5] if member.image]

    return {
        "created_time": event.created_at,
        "event_date": event.start_date,
        "event_time": event.start_time,
        "event_type": event.event_type,
        "event_location": event.event_location,
        "industry": event.industry,
        "team": {
            "team_name": team_name,
            "member_images": team_members,
        },
        "team_lead": {
            "email": team_lead_email,
        }
    }

async def add_guest_tag(tag_data: schemas.GuestTagCreate, db: AsyncSession):
    try:
        new_tag = models.GuestTag(
            tag_name=tag_data.tag_name,
            event_id=tag_data.event_id,
            rsvp_form=tag_data.rsvp_form,
            name_badge_printing=tag_data.name_badge_printing,
            broadcast_id=tag_data.broadcast_id
        )
        db.add(new_tag)
        await db.commit()
        await db.refresh(new_tag)
        return new_tag
    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add guest tag: {str(e)}"
        )

async def get_guest_tag_info(guest_tag_id: UUID, db: AsyncSession):
    # Fetch the GuestTag and related Event
    result = await db.execute(
        select(
            models.GuestTag.guest_tag_id,
            models.GuestTag.event_id,
            models.Event.event_name,
            func.count(models.Guest.guest_id).label("total_guests"),
            func.count(func.nullif(models.Guest.checked_in == False, True)).label("checked_in_count")
        )
        .join(models.Event, models.GuestTag.event_id == Event.event_id)
        .outerjoin(models.Guest, models.Guest.guest_tag_id == models.GuestTag.guest_tag_id)
        .where(models.GuestTag.guest_tag_id == guest_tag_id)
        .group_by(models.GuestTag.guest_tag_id, models.Event.event_name, models.GuestTag.event_id)
    )

    row = result.first()

    if not row:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Guest tag not found"
        )

    return {
        "event_id": row.event_id,
        "event_name": row.event_name,
        "total_guests": row.total_guests,
        "total_checked_in": row.checked_in_count
    }

async def get_guest_tag_overview(guest_tag_id: UUID, db: AsyncSession):
    # Fetch guest tag with related guests and event
    result = await db.execute(
        select(
            models.GuestTag.guest_tag_id,
            models.GuestTag.event_id,
            models.Event.event_name,
            models.Guest.first_name,
            models.Guest.last_name,
            models.Guest.external_id,
            models.Guest.rsvp,
            models.Guest.checked_in
        )
        .join(models.Event, models.GuestTag.event_id == Event.event_id)
        .join(models.Guest, models.Guest.guest_tag_id == models.GuestTag.guest_tag_id)
        .where(models.GuestTag.guest_tag_id == guest_tag_id)
    )

    rows = result.fetchall()

    if not rows:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No data found for the given guest tag"
        )

    event_info = {
        "event_id": rows[0].event_id,
        "event_name": rows[0].event_name,
        "guests": [
            {
                "guest_name": f"{row.first_name} {row.last_name}",
                "external_id": row.external_id,
                "rsvp": row.rsvp,
                "checked_in": row.checked_in
            } for row in rows
        ]
    }

    return event_info

async def add_guest_to_guest_tag(guest_data: schemas.GuestCreate, db: AsyncSession):
    new_guest = models.Guest(
        guest_id=uuid4(),
        salutation=guest_data.salutation,
        first_name=guest_data.first_name,
        last_name=guest_data.last_name,
        email=guest_data.email,
        phone=guest_data.phone,
        seating_style=guest_data.seating_style,
        color=guest_data.color,
        external_id=guest_data.external_id,
        affiliation=guest_data.affiliation,
        secondary_email=guest_data.secondary_email,
        note=guest_data.note,
        guest_tag_id=guest_data.guest_tag_id
    )

    try:
        db.add(new_guest)
        await db.commit()
        await db.refresh(new_guest)
        return new_guest
    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

async def add_notification_setting(data: schemas.NotificationSettingCreate, db: AsyncSession):
    new_setting = models.NotificationSetting(
        email_recipients=data.email_recipients,
        text_recipients=data.text_recipients,
        guest_id=data.guest_id
    )

    try:
        db.add(new_setting)
        await db.commit()
        await db.refresh(new_setting)
        return new_setting
    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

async def get_a_guest_tag_overview(guest_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(models.Guest)
        .where(models.Guest.guest_id == guest_id)
        .options(
            # Optionally join GuestTag if needed for efficiency
            joinedload(models.Guest.guest_tag)
        )
    )
    guest = result.scalar_one_or_none()

    if not guest:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Guest not found")

    return {
        "guest_tag_name": guest.guest_tag.tag_name if guest.guest_tag else None,
        "guest_tag_id": str(guest.guest_tag_id) if guest.guest_tag_id else None,
        "external_id": guest.external_id,
        "rsvp": guest.rsvp,
        "checked_in": guest.checked_in,
    }

async def get_a_guest_overview(guest_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(models.Guest).where(models.Guest.guest_id == guest_id)
    )
    guest = result.scalar_one_or_none()

    if not guest:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Guest not found")

    return {
        "salutation": guest.salutation,
        "first_name": guest.first_name,
        "last_name": guest.last_name,
        "email": guest.email,
        "start_date": guest.created_at,
        "seating_style": guest.seating_style,
        "color": guest.color,
        "guest_list": guest.guest_tag.tag_name if guest.guest_tag else None,
        "external_id": guest.external_id,
        "affiliation": guest.affiliation,
        "secondary_email": guest.secondary_email,
        "note": guest.note,
    }


async def upload_guests(file: UploadFile, db: AsyncSession):
    try:
        content = await file.read()

        # Determine file type
        if file.filename.endswith(".csv"):
            df = pd.read_csv(BytesIO(content))
        elif file.filename.endswith((".xls", ".xlsx")):
            df = pd.read_excel(BytesIO(content))
        else:
            raise HTTPException(status_code=400, detail="Unsupported file format")

        # Validate required columns
        required_columns = {"first_name", "last_name", "email"}
        if not required_columns.issubset(df.columns):
            raise HTTPException(status_code=400, detail=f"Missing required columns: {required_columns - set(df.columns)}")

        # Save to database
        for _, row in df.iterrows():
            guest = Guest(
                guest_id=uuid4(),
                salutation=row.get("salutation"),
                first_name=row["first_name"],
                last_name=row["last_name"],
                email=row["email"],
                phone=row.get("phone"),
                seating_style=row.get("seating_style"),
                color=row.get("color"),
                external_id=row.get("external_id"),
                affiliation=row.get("affiliation"),
                secondary_email=row.get("secondary_email"),
                note=row.get("note"),
                guest_tag_id=row.get("guest_tag_id"),
                created_at=datetime.utcnow()
            )
            db.add(guest)

        await db.commit()
        return {"message": "Guests uploaded successfully"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

TEMPLATE_COLUMNS = [
    "salutation",
    "first_name",
    "last_name",
    "email",
    "phone",
    "seating_style",
    "color",
    "external_id",
    "affiliation",
    "secondary_email",
    "note",
    "guest_tag_id"
]

def download_guest_csv():
    df = pd.DataFrame(columns=TEMPLATE_COLUMNS)
    stream = StringIO()
    df.to_csv(stream, index=False)
    stream.seek(0)

    return StreamingResponse(
        iter([stream.getvalue()]),
        media_type="text/csv",
        headers={"Content-Disposition": "attachment; filename=guest_template.csv"}
    )

def download_guest_excel():
    df = pd.DataFrame(columns=TEMPLATE_COLUMNS)
    stream = BytesIO()
    with pd.ExcelWriter(stream, engine='openpyxl') as writer:
        df.to_excel(writer, index=False, sheet_name="GuestTemplate")
    stream.seek(0)

    return StreamingResponse(
        stream,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": "attachment; filename=guest_template.xlsx"}
    )


async def create_guest_form_to_add_to_tag(
    data: schemas.GuestSimpleCreate, db: AsyncSession
) -> models.Guest:
    new_guest = models.Guest(
        salutation=data.salutation,
        first_name=data.first_name,
        last_name="",  # Set default or modify schema to accept last name
        email=data.email,
        phone=data.phone,
        guest_tag_id=data.guest_tag_id
    )
    db.add(new_guest)
    await db.commit()
    await db.refresh(new_guest)
    return new_guest

async def get_detail_for_guest_form(guest_id: UUID, db: AsyncSession) -> schemas.GuestFormDetailOut:
    result = await db.execute(
        select(models.Guest).where(models.Guest.guest_id == guest_id)
        .join(models.Guest.guest_tag)
        .join(models.Guest.guest_tag.of_type(models.Guest.guest_tag).event)
    )
    guest = result.scalar_one_or_none()

    if not guest or not guest.guest_tag or not guest.guest_tag.event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Guest or related event not found"
        )

    return schemas.GuestFormDetailOut(
        event_name=guest.guest_tag.event.event_name,
        event_description=guest.guest_tag.event.event_description,
        email=guest.email
    )


async def print_added_guest_on_event(event_id: UUID, db: AsyncSession) -> list[schemas.GuestOut]:
    # Get all GuestTags for the event
    result = await db.execute(
        select(models.Guest).join(models.Guest.guest_tag)
        .where(models.GuestTag.event_id == event_id)
    )
    guests = result.scalars().all()

    if not guests:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No guests found for this event"
        )

    return guests


async def get_upcoming_events(db: AsyncSession) -> List[schemas.EventOut]:
    now = datetime.utcnow()
    result = await db.execute(
        select(models.Event).where(models.Event.start_date > now).order_by(models.Event.start_date.asc())
    )
    return result.scalars().all()

async def get_past_events(db: AsyncSession) -> List[schemas.EventOut]:
    now = datetime.utcnow()
    result = await db.execute(
        select(models.Event).where(models.Event.start_date < now).order_by(models.Event.start_date.desc())
    )
    return result.scalars().all()