from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import dashboard

router = APIRouter(prefix="/dashboard", tags=["dashboard"])

@router.get("/projects/total")
async def total_projects(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    total = await dashboard.get_total_projects(db,current_user.email)
    return {"total_projects": total}

@router.get("/events/total")
async def total_events(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    total = await dashboard.get_total_events(db, current_user.email)
    return {"total_events": total}

@router.get("/tasks/total")
async def total_tasks(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    total = await dashboard.get_total_tasks(db,current_user.user_id)
    return {"total_tasks": total}

@router.get(
    "/events/recent",
    response_model=List[schemas.RecentEvent],
    summary="Get 6 most recent events created by a user",
    description="Fetches the first 6 events created by the specified user along with their project names."
)
async def get_recent_events(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
) -> List[schemas.RecentEvent]:
    """
    Returns up to 6 most recently created events by a user, including the linked project name.
    """
    return await dashboard.get_recent_events_by_user(current_user.user_id, db)

@router.get(
    "/tasks/recent",
    response_model=List[schemas.RecentTask],
    summary="Get 2 most recent tasks created by a user",
    description="Fetches the first 2 tasks created by the specified user along with project and event details."
)
async def get_recent_tasks(
   db: AsyncSession = Depends(get_session),
   current_user: schemas.UserOut = Depends(get_current_active_user)
) -> List[schemas.RecentTask]:
    """
    Returns up to 2 most recently created tasks by a user, including linked project and event names.
    """
    return await dashboard.get_recent_tasks_by_user(current_user.user_id, db)

@router.get(
    "/teams/chats",
    response_model=List[schemas.TeamChatSummary],
    summary="Get 2 most recent team chats by user",
    description="Fetches the first 2 chat sessions of teams that the user belongs to, with member stats."
)
async def get_team_chats(
   db: AsyncSession = Depends(get_session),
   current_user: schemas.UserOut = Depends(get_current_active_user)
) -> List[schemas.TeamChatSummary]:
    """
    Returns up to 2 most recent team chat sessions for a user, including member images, lead count, and total members.
    """
    return await dashboard.get_team_chats_by_user(current_user.user_id, db)

@router.get(
    "/teams/leads/events",
    response_model=List[schemas.TeamLeadEvent],
    summary="Get 3 events where user is team lead",
    description="Fetches the first three events for which the user holds a lead role, including team, project, and event info."
)
async def get_team_lead_events(
   db: AsyncSession = Depends(get_session),
   current_user: schemas.UserOut = Depends(get_current_active_user)
) -> List[schemas.TeamLeadEvent]:
    """
    Returns up to 3 events where the user is a team lead, with user image, email, team name, project name, and event name.
    """
    return await dashboard.get_team_leads(current_user.user_id, db)