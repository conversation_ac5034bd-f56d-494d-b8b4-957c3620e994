from src.utils.commonImports import *
from src.utils.commonSession import get_session
from src.crud.users import get_current_active_user
from src.schemas import schemas
from src.crud import team

router = APIRouter(prefix="/team", tags=["team"])

@router.post("/teams", response_model=schemas.TeamResponse)
async def create_team(
    team: schemas.TeamCreate, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await team.add_team(db, team)

@router.post("/teams/members", response_model=schemas.TeamMemberResponse)
async def create_team_member(
    member: schemas.TeamMemberCreate, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await add_team_member_to_team(db, member) 

@router.get("/teams/{team_id}/members/overview", response_model=schemas.TeamMembersOverviewResponse)
async def team_members_overview(
    team_id: UUID, 
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
    ):
    return await team.get_team_members_overview(db, team_id) 

@router.get("/teams/analysis", response_model= schemas.TeamAnalysisResponse)
async def team_analysis(
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await team.get_team_analysis(db, current_user) 

@router.get("/teams/{team_id}/analysis", response_model=schemas.SingleTeamAnalysisResponse)
async def single_team_analysis(
    team_id: UUID,
    db: AsyncSession = Depends(get_session),
    current_user: schemas.UserOut = Depends(get_current_active_user)
):
    return await team.get_single_team_analysis(db, team_id)    