from src.utils.config import settings
import httpx
from fastapi import HTTPException
import logging

# Set up a basic logger for error logging
logger = logging.getLogger(__name__)

# Helper function to handle SMS API responses
def handle_sms_api_response(data):
    """
    Handle the response from the SMS API, checking for success or known errors.
    """
    status = data.get("response", {}).get("status", "UNKNOWN_ERROR")
    
    if status == "SUCCESS":
        return {
            "status": "success",
            "message": "Operation successful",
            "totalsent": data.get("response", {}).get("totalsent", "unknown"),
            "cost": data.get("response", {}).get("cost", "unknown")
        }

    known_errors = {
        "INVALID_JSON": "Invalid JSON format.",
        "MISSING_USERNAME": "Username field is empty.",
        "MISSING_APIKEY": "API key field is empty.",
        "AUTH_FAILURE": "Incorrect username and API key combination.",
        "MISSING_SENDER": "Sender name is missing.",
        "MISSING_MESSAGE": "Message content is empty.",
        "MISSING_RECIPIENT": "No mobile phone number found.",
        "INVALID_RECIPIENT": "Invalid mobile phone number provided.",
        "INVALID_MESSAGE": "Message is too long or contains unsupported characters.",
        "INVALID_SENDER": "Sender name is invalid or missing.",
        "INSUFFICIENT_CREDIT": "Insufficient credit to send the message.",
        "UNKNOWN_CONTENTTYPE": "Ensure Content-Type header is 'application/json' or 'text/json'.",
        "UNKNOWN_ERROR": "An unknown error occurred."
    }
    
    error_message = known_errors.get(status, "An unknown error occurred.")
    raise HTTPException(status_code=400, detail=f"Failed to send SMS: {error_message}")

# Function to check SMS wallet balance via EbulkSMS
async def check_sms_wallet_balance():
    """
    Check the balance in an SMS wallet.
    """
    try:
        url = f"https://api.ebulksms.com/balance/{settings.EBULKSMS_USERNAME}/{settings.EBULKSMS_API_KEY}"

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(url)

        if response.status_code == 200:
            try:
                # Directly use the response text, which is a float
                balance = float(response.text.strip())
                logger.info(f"SMS Wallet Balance: {balance}")

                if balance < 0:
                    raise HTTPException(status_code=400, detail="SMS balance is negative or unavailable.")
                
                return balance  # Return the balance directly

            except ValueError:
                # Handle case where the response is not a valid float
                logger.error(f"Error parsing balance response: {response.text}")
                raise HTTPException(status_code=500, detail="Failed to parse SMS balance response.")

        else:
            raise HTTPException(status_code=500, detail=f"Failed to check SMS balance status: {response.status_code}")

    except Exception as e:
        # General error handling for any other issues (like connection issues)
        logger.error(f"Error checking SMS wallet balance: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An error occurred while checking the SMS balance: {str(e)}")
  
async def send_otp_sms(phone_numbers: str, otp_message: str):
    """
    Send OTP via SMS using EbulkSMS API, checking wallet balance first.
    """
    try:
        required_balance = 10.0
        balance = await check_sms_wallet_balance()
        if balance <= required_balance:
            raise HTTPException(status_code=400, detail="Insufficient wallet balance to send SMS")

        payload = {
            "SMS": {
                "auth": {
                    "username": settings.EBULKSMS_USERNAME,
                    "apikey": settings.EBULKSMS_API_KEY
                },
                "message": {
                    "sender": "Tplanner",
                    "messagetext": otp_message,
                    "flash": "0"
                },
                "recipients": {
                    "gsm": [{"msidn": phone_numbers, "msgid": "uniqueid1"}]
                },
                "dndsender": 1
            }
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post("https://api.ebulksms.com/sendsms.json", json=payload)

        if response.status_code == 200:
            data = response.json()
            return handle_sms_api_response(data)

        raise HTTPException(status_code=500, detail=f"Failed to send OTP: HTTP {response.status_code}")

    except Exception as e:
        logger.error(f"Error sending OTP SMS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An error occurred while sending OTP: {str(e)}")


async def send_sms_invite(phone_numbers: str, invite_link: str, team_name: str):
    try:
        required_balance = 10.0
        balance = await check_sms_wallet_balance()
        if balance <= required_balance:
            raise HTTPException(status_code=400, detail="Insufficient wallet balance to send SMS")

        invite_message = f"You're invited to join {team_name} team on timeless planner. Click here: {invite_link}"
        phone_number_list = phone_numbers.split(',')
        gsm_list = [{"msidn": phone.strip(), "msgid": f"tplannerv_{idx}"} for idx, phone in enumerate(phone_number_list, 1)]

        payload = {
            "SMS": {
                "auth": {
                    "username": settings.EBULKSMS_USERNAME,
                    "apikey": settings.EBULKSMS_API_KEY
                },
                "message": {
                    "sender": "Tplanner",
                    "messagetext": invite_message,
                    "flash": "0"
                },
                "recipients": {
                    "gsm": gsm_list  # Fixed here
                },
                "dndsender": 1
            }
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post("https://api.ebulksms.com/sendsms.json", json=payload)

        if response.status_code == 200:
            data = response.json()
            return handle_sms_api_response(data)

        raise HTTPException(status_code=500, detail=f"Failed to send SMS: HTTP {response.status_code}")

    except Exception as e:
        logger.error(f"Error sending invite SMS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An error occurred while sending SMS: {str(e)}")


async def send_notification_sms(contacts: str, message: str):
    """
    Send a notification SMS to a group of contacts using EbulkSMS API.
    """
    try:
        balance = await check_sms_wallet_balance()
        if balance < 20:
            raise HTTPException(status_code=400, detail="Insufficient wallet balance to send SMS")

        phone_number_list = contacts.split(',')
        gsm_list = [{"msidn": phone.strip(), "msgid": f"tplannerv_{idx}"} for idx, phone in enumerate(phone_number_list, 1)]

        payload = {
            "SMS": {
                "auth": {
                    "username": settings.EBULKSMS_USERNAME,
                    "apikey": settings.EBULKSMS_API_KEY
                },
                "message": {
                    "sender": "Tplanner",
                    "messagetext": message,
                    "flash": "0"
                },
                "recipients": {
                    "gsm": gsm_list
                },
                "dndsender": 1
            }
        }

        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post("https://api.ebulksms.com/sendsms.json", json=payload)

        if response.status_code == 200:
            data = response.json()
            return handle_sms_api_response(data)

        raise HTTPException(status_code=500, detail=f"Failed to send notification SMS: HTTP {response.status_code}")

    except Exception as e:
        logger.error(f"Error sending notification SMS: {str(e)}")
        raise HTTPException(status_code=500, detail=f"An error occurred while sending the notification: {str(e)}")
